"use client";

import React from "react";
import check_cancel from "../../assets/images/check_cancel.png";
import check_correct from "../../assets/images/check_correct.png";
import Image from "next/image";
const ProductHighlightsCard = ({ productDetails }) => {
  return (
    <div className="bg-black text-white rounded-2xl p-6 w-full">
      <h2 className="text-2xl font-semibold tamb-4 transformaSansBold">Pros & Cons</h2>

      <div className="mb-6 Sora">
        <h3 className="text-[#E0F069] font-semibold mb-2">Advantages</h3>
        <ul className="space-y-2">
          {productDetails?.prosAndCons?.pros?.map((item, index) => (
            <li key={index} className="flex items-start gap-2">
              <Image src={check_cancel} className="w-4 h-4 my-auto" />
              <span>{item}</span>
            </li>
          ))}
        </ul>
      </div>

      <div className="Sora">
        <h3 className="text-[#F0381A] font-semibold mb-2">Limitations</h3>
        <ul className="space-y-2">
          {productDetails?.prosAndCons?.cons?.map((item, index) => (
            <li key={index} className="flex items-start gap-2">
              <Image src={check_correct} className="w-4 h-4 my-auto" />
              <span>{item}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ProductHighlightsCard;
