import { db } from "./db";
import { sql } from "drizzle-orm";
import dotenv from "dotenv";

dotenv.config();

const main = async () => {
  try {
    // Check if isActive column exists
    const checkIsActiveResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND column_name = 'isActive'
    `);

    // If isActive column doesn't exist, add it
    if (checkIsActiveResult.rows.length === 0) {
      console.log("Adding 'isActive' column to user table...");
      await db.execute(sql`
        ALTER TABLE "user"
        ADD COLUMN "isActive" boolean DEFAULT true NOT NULL
      `);
      console.log("✅ Successfully added 'isActive' column to user table!");
    } else {
      console.log("'isActive' column already exists in user table.");
    }

    // Check if updatedAt column exists
    const checkUpdatedAtResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND column_name = 'updatedAt'
    `);

    // If updatedAt column doesn't exist, add it
    if (checkUpdatedAtResult.rows.length === 0) {
      console.log("Adding 'updatedAt' column to user table...");
      await db.execute(sql`
        ALTER TABLE "user"
        ADD COLUMN "updatedAt" timestamp DEFAULT NOW() NOT NULL
      `);
      console.log("✅ Successfully added 'updatedAt' column to user table!");
    } else {
      console.log("'updatedAt' column already exists in user table.");
    }

    // Check if role column exists
    const checkRoleResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND column_name = 'role'
    `);

    // If role column doesn't exist, add it
    if (checkRoleResult.rows.length === 0) {
      console.log("Adding 'role' column to user table...");
      await db.execute(sql`
        ALTER TABLE "user"
        ADD COLUMN "role" text DEFAULT 'customer' NOT NULL
      `);
      console.log("✅ Successfully added 'role' column to user table!");
    } else {
      console.log("'role' column already exists in user table.");
    }

    // Rename createdAt column if it's still created_at
    const checkCreatedAtResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND column_name = 'created_at'
    `);

    if (checkCreatedAtResult.rows.length > 0) {
      console.log("Renaming 'created_at' column to 'createdAt'...");
      await db.execute(sql`
        ALTER TABLE "user"
        RENAME COLUMN "created_at" TO "createdAt"
      `);
      console.log(
        "✅ Successfully renamed 'created_at' column to 'createdAt'!"
      );
    }

    // Drop status column if it exists
    const checkStatusResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND column_name = 'status'
    `);

    if (checkStatusResult.rows.length > 0) {
      console.log("Dropping 'status' column from user table...");
      await db.execute(sql`
        ALTER TABLE "user"
        DROP COLUMN "status"
      `);
      console.log("✅ Successfully dropped 'status' column from user table!");
    }

    // Check if loginMethod column exists
    const checkLoginMethodResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND column_name = 'loginMethod'
    `);

    // If loginMethod column doesn't exist, add it
    if (checkLoginMethodResult.rows.length === 0) {
      console.log("Adding 'loginMethod' column to user table...");
      await db.execute(sql`
        ALTER TABLE "user"
        ADD COLUMN "loginMethod" text DEFAULT 'manual' NOT NULL
      `);
      console.log("✅ Successfully added 'loginMethod' column to user table!");
    } else {
      console.log("'loginMethod' column already exists in user table.");
    }

    console.log("✅ Users table migration completed successfully!");
  } catch (error) {
    console.error("❌ Error during migration:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
};

main();
