"use client";

import React, { useRef, useEffect } from "react";
import { IoSearchOutline } from "react-icons/io5";
import useVectorSearch from "@/hooks/useVectorSearch";
import SearchResults from "../SearchResults";

const VectorSearchBar = ({ customWidth, customPadding, customBorder }) => {
  const {
    searchTerm,
    searchResults,
    isLoading,
    isError,
    showResults,
    searchType,
    setShowResults,
    handleSearchChange,
    handleSearchSubmit,
    handleResultClick,
    handleSearchTypeChange,
  } = useVectorSearch();

  const searchRef = useRef(null);

  // Handle clicking outside to close results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [setShowResults]);

  const searchTypeOptions = [
    {
      value: "text",
      label: "Text Search",
      description: "Traditional keyword search",
    },
    {
      value: "vector",
      label: "Vector Search",
      description: "AI-powered semantic search",
    },
    {
      value: "hybrid",
      label: "Hybrid Search",
      description: "Combined text + vector search",
    },
  ];

  return (
    <div className="relative w-full" ref={searchRef}>
      {/* Search Type Selector */}
      <div className="mb-2">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Search Type
        </label>
        <div className="flex gap-2 flex-wrap">
          {searchTypeOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => handleSearchTypeChange(option.value)}
              className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                searchType === option.value
                  ? "bg-[#F0381A] text-white border-[#F0381A]"
                  : "bg-white text-gray-600 border-gray-300 hover:border-[#F0381A]"
              }`}
              title={option.description}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Search Input */}
      <form onSubmit={handleSearchSubmit} className="relative">
        <div
          className={`flex items-center bg-white border border-gray-300 rounded-lg overflow-hidden transition-all duration-200 ${
            showResults ? "rounded-b-none border-b-0" : ""
          } ${customBorder || ""}`}
          style={{
            width: customWidth || "100%",
            padding: customPadding || "0",
          }}
        >
          <input
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder={`Search products using ${searchType} search...`}
            className="flex-1 px-4 py-3 text-gray-700 bg-transparent outline-none placeholder-gray-400"
            autoComplete="off"
          />
          <button
            type="submit"
            className="px-4 py-3 text-gray-500 hover:text-[#F0381A] transition-colors"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#F0381A]"></div>
            ) : (
              <IoSearchOutline size={20} />
            )}
          </button>
        </div>

        {/* Search Type Indicator */}
        {searchTerm && (
          <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
            <span
              className={`text-xs px-2 py-1 rounded-full ${
                searchType === "vector"
                  ? "bg-purple-100 text-purple-600"
                  : searchType === "hybrid"
                  ? "bg-blue-100 text-blue-600"
                  : "bg-gray-100 text-gray-600"
              }`}
            >
              {searchType}
            </span>
          </div>
        )}
      </form>

      {/* Search Results */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 z-50">
          <SearchResults
            searchResults={searchResults}
            isLoading={isLoading}
            isError={isError}
            onResultClick={handleResultClick}
            searchType={searchType}
          />
        </div>
      )}

      {/* Search Type Information */}
      {searchTerm && (
        <div className="mt-2 text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <span>Using:</span>
            <span className="font-medium">
              {
                searchTypeOptions.find((opt) => opt.value === searchType)
                  ?.description
              }
            </span>
            {searchType === "vector" && (
              <span className="text-purple-600">✨ AI-powered</span>
            )}
            {searchType === "hybrid" && (
              <span className="text-blue-600">🔄 Best of both</span>
            )}
            {/* Show actual search metadata if available */}
            {searchResults?.data?.search_metadata && (
              <span className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs">
                {searchResults.data.search_metadata.note}
                {searchResults.fallback && " (fallback)"}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VectorSearchBar;
