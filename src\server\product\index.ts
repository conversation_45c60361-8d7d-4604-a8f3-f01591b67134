"use server";

import { sql } from "drizzle-orm";
import { db } from "@/lib/db";
import { products } from "@/lib/schema";

export async function getProductByName(name: string) {
  const cleanedName = name.replace(/[^a-zA-Z0-9]/g, "");

  try {
    const result = await db
      .select()
      .from(products)
      .where(
        sql`REGEXP_REPLACE(LOWER(${
          products.name
        }), '[^a-z0-9]', '', 'g') ILIKE ${
          "%" + cleanedName.toLowerCase() + "%"
        }`
      )
      .limit(1);

    return result[0];
  } catch (error) {
    console.error("Error fetching product:", error);
    throw new Error("Could not fetch product");
  }
}
