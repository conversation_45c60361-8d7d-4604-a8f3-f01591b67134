import React from "react";
import { Metadata } from "next";
import SearchPage from "./SearchPage";
import { getAllCategoriesForSearch } from "@/server/search";

interface SearchPageProps {
  searchParams: { q?: string };
}

export const metadata: Metadata = {
  title: "Search Products - Pricio",
  description: "Search for products and compare prices on Pricio",
};

export default async function SearchPageWrapper({
  searchParams,
}: SearchPageProps) {
  const params = await Promise.resolve(searchParams);
  const query = params.q || "";
  const allCategories = await getAllCategoriesForSearch();
  console.log("Search page received query param:", query);

  return <SearchPage initialQuery={query} allCategories={allCategories} />;
}
