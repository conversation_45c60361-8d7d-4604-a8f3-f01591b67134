"use server";

import { db } from "@/lib/db";
import { categories } from "@/lib/schema";
import { isNull } from "drizzle-orm";

export async function getCategories() {
  try {
    return await db
      .select()
      .from(categories)
      .where(isNull(categories.parentId));
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw new Error("Could not fetch categories");
  }
}
