import Image from "next/image";
import React from "react";

const CustomButton = ({
  label,
  paddingY = null,
  borderCustom = null,
  bgColor = null,
  textColor = null,
  icon: Icon,
  iconR: IconR,
  onClickButton,
  disableField,
  Isrelative = null,
  tooltipTitle = null,
  LoadingBtn = null,
  loadingText = null,
  iconWidth = null,
  iconHeight = null,
  noMarginIcon = null,
  textSize = null,
  textMarginBotton = null,
  widthButton = null,
  marginTop = null,
  autoLeftMargin = null,
  paddingx = null,
  justifyContent = null,
  id,
  imageLeft = null,
  buttonType = null,
}) => {
  return (
    <button
      id={id}
      className={`inline-flex items-center 
                ${marginTop && marginTop}
                ${Isrelative ? "relative" : null}
                ${borderCustom && borderCustom} 
                ${justifyContent ? justifyContent : "justify-center"} 
                ${paddingx ? paddingx : "px-5"} 
                ${paddingY ? paddingY : "py-2"}
                leading-[2] mb-1 
                ${textSize ? textSize : "text-sm"} 
                ${widthButton ? widthButton : "w-full"} 
                ${bgColor && bgColor}
                ${textColor && textColor}
                font-medium rounded cursor-pointer Sora`}
      type={buttonType ? buttonType : "submit"}
      onClick={onClickButton}
      disabled={disableField}
      title={tooltipTitle}
    >
      {LoadingBtn ? (
        <span className="flex items-center justify-center font-bold">
          <span className={`${loadingText && "ml-2"}`}>{loadingText}</span>
        </span>
      ) : (
        <>
          {imageLeft && (
            <Image
              src={imageLeft}
              width={iconWidth}
              height={iconHeight}
              alt="icon"
              className={`my-auto ${noMarginIcon ? "mr-0" : "ml-2"} ${
                autoLeftMargin && autoLeftMargin
              } `}
            />
          )}
          {Icon && (
            <Icon
              className={`my-auto ${iconWidth ? `w-${iconWidth}` : "w-5"} ${
                iconHeight ? `h-${iconHeight}` : "w-5"
              } ${noMarginIcon ? "mr-0" : "mr-2"}`}
            />
          )}
          <span className={`${textMarginBotton && textMarginBotton}`}>
            {label}
          </span>
          {IconR && (
            <IconR
              className={`my-auto ${iconWidth ? `w-${iconWidth}` : "w-5"} ${
                iconHeight ? `h-${iconHeight}` : "w-5"
              } ${noMarginIcon ? "mr-0" : "ml-2"} ${
                autoLeftMargin && autoLeftMargin
              } `}
            />
          )}
        </>
      )}
    </button>
  );
};

export default CustomButton;
