"use client";

import { useState, useCallback, useMemo } from "react";
export default function useTypesenseFilters(initialFilters = {}) {
  const [selectedFilters, setSelectedFilters] = useState(initialFilters);
  const [categories, setCategories] = useState([]);
  const [priceRanges, setPriceRanges] = useState([]);
  const [searchData, setSearchData] = useState(null);

  const updateFacets = useCallback((searchResults) => {
    if (searchResults?.hits && searchResults.hits.length > 0) {
      const categoryMap = new Map();
      searchResults.hits.forEach((hit) => {
        if (hit.document.category_name) {
          categoryMap.set(hit.document.category_name, {
            name: hit.document.category_name,
            slug: hit.document.category_slug || "",
            id: hit.document.category_id || "",
          });
        }
      });
      setCategories(Array.from(categoryMap.values()));
      setSearchData(searchResults);
    }
  }, []);

  const toggleFilter = useCallback((facetName, value) => {
    setSelectedFilters((prev) => {
      const newFilters = { ...prev };
      if (!newFilters[facetName]) {
        newFilters[facetName] = [];
      }
      const valueIndex = newFilters[facetName].indexOf(value);

      if (valueIndex === -1) {
        // Add the value if it's not already selected
        newFilters[facetName] = [...newFilters[facetName], value];
      } else {
        // Remove the value if it's already selected
        newFilters[facetName] = newFilters[facetName].filter(
          (v) => v !== value
        );

        // Remove the facet if there are no values selected
        if (newFilters[facetName].length === 0) {
          delete newFilters[facetName];
        }
      }

      return newFilters;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setSelectedFilters({});
  }, []);

  const clearFacetFilters = useCallback((facetName) => {
    setSelectedFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[facetName];
      return newFilters;
    });
  }, []);

  const isFilterSelected = useCallback(
    (facetName, value) => {
      return (
        selectedFilters[facetName] && selectedFilters[facetName].includes(value)
      );
    },
    [selectedFilters]
  );

  // This function is now primarily for reference and backward compatibility
  // The actual filtering is now done server-side in Typesense
  const getFilteredResults = useCallback(() => {
    if (!searchData || !searchData.hits || searchData.hits.length === 0) {
      return { hits: [], found: 0 };
    }

    // If no filters selected, return all results
    if (Object.keys(selectedFilters).length === 0) {
      return searchData;
    }

    // Check if we only have price filters (which are now handled server-side)
    const hasOnlyPriceFilters =
      Object.keys(selectedFilters).length === 1 &&
      Object.keys(selectedFilters).includes("price");

    if (hasOnlyPriceFilters) {
      // Price filters are already applied by Typesense, return the results as is
      return searchData;
    }

    // Filter the hits based on selected filters
    const filteredHits = searchData.hits.filter((hit) => {
      // Check each filter type
      for (const [filterType, values] of Object.entries(selectedFilters)) {
        if (values.length === 0) continue;

        // Category filter
        if (filterType === "category_name") {
          if (!values.includes(hit.document.category_name)) {
            return false;
          }
        }

        // We don't need to handle price filters here anymore
        // They are now handled by Typesense server-side
      }

      // If we get here, the hit matches all filters
      return true;
    });

    // Return filtered results in the same format as the original data
    return {
      ...searchData,
      hits: filteredHits,
      found: filteredHits.length,
    };
  }, [searchData, selectedFilters]);

  /**
   * Generate price range facets for Typesense
   */
  const generatePriceRangeFacets = useCallback(() => {
    const priceRanges = [
      { min: 0, max: 15000, label: "Below Rs. 15,000" },
      { min: 15000, max: 25000, label: "Rs. 15,000 - Rs. 25,000" },
      { min: 25000, max: 40000, label: "Rs. 25,000 - Rs. 40,000" },
      { min: 40000, max: 60000, label: "Rs. 40,000 - Rs. 60,000" },
      { min: 60000, max: 80000, label: "Rs. 60,000 - Rs. 80,000" },
      { min: 80000, max: 100000, label: "Rs. 80,000 - Rs. 100,000" },
      { min: 100000, max: 150000, label: "Rs. 100,000 - Rs. 150,000" },
      { min: 150000, max: null, label: "Above Rs. 150,000" },
    ];

    return priceRanges.map((range) => {
      // For client-side filtering, we need a value format that's easy to parse
      const value = range.max ? `${range.min}-${range.max}` : `${range.min}-`;

      // For Typesense facet queries (if we use them in the future)
      const facetQuery = range.max
        ? `price:=[${range.min}..${range.max}]`
        : `price:>=${range.min}`;

      return {
        value,
        label: range.label,
        facetQuery,
        min: range.min,
        max: range.max,
        count: 0,
      };
    });
  }, []);

  const getPriceRangesWithCounts = useCallback(() => {
    const priceRanges = generatePriceRangeFacets();

    if (!searchData || !searchData.hits || searchData.hits.length === 0) {
      return priceRanges.map((range) => ({
        ...range,
        count: 0,
        displayLabel: `${range.label} (0)`,
      }));
    }

    // Count products in each price range
    return priceRanges.map((range) => {
      const [minStr, maxStr] = range.value.split("-");
      const min = parseFloat(minStr);
      const max = maxStr ? parseFloat(maxStr) : Infinity;
      const count = searchData.hits.filter((hit) => {
        if (!hit.document || typeof hit.document.price !== "number") {
          return false;
        }
        const price = hit.document.price;
        return price >= min && (maxStr === "" || price <= max);
      }).length;

      // If this price range is currently selected, we should show the actual count
      // from the filtered results instead of 0
      const isRangeSelected =
        selectedFilters.price && selectedFilters.price.includes(range.value);

      return {
        ...range,
        count,
        // Add a formatted display label with count
        // If this range is selected, we'll show the count from the filtered results
        displayLabel: `${range.label}`,
        isSelected: isRangeSelected,
      };
    });
  }, [searchData, generatePriceRangeFacets, selectedFilters.price]);

  return {
    selectedFilters,
    categories,
    priceRanges: getPriceRangesWithCounts(),
    updateFacets,
    toggleFilter,
    clearFilters,
    clearFacetFilters,
    isFilterSelected,
    getFilteredResults,
    generatePriceRangeFacets,
  };
}
