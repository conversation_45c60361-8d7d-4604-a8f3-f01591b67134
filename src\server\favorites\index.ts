"use server";

import { db } from "@/lib/db";
import { favorites, products } from "@/lib/schema";
import { isAuthenticated } from "@/lib/auth";
import { and, eq } from "drizzle-orm";
import { successResponse, errorResponse } from "@/lib/common/responses";

export async function addToFavorites(productId: number) {
  try {
    const user = await isAuthenticated();

    if (!user) {
      return errorResponse("You must be logged in to add favorites", 401);
    }

    const existingFavorite = await db
      .select()
      .from(favorites)
      .where(
        and(eq(favorites.userId, user.id), eq(favorites.productId, productId))
      );

    if (existingFavorite.length > 0) {
      return successResponse(null, "Product is already in favorites", 200);
    }

    // Add to favorites
    await db.insert(favorites).values({
      userId: user.id,
      productId: productId,
    });

    return successResponse(null, "Product added to favorites", 201);
  } catch (error) {
    console.error("Error adding to favorites:", error);
    return errorResponse("Failed to add product to favorites", 500);
  }
}

/**
 * Remove a product from the user's favorites
 */
export async function removeFromFavorites(productId: number) {
  try {
    const user = await isAuthenticated();

    if (!user) {
      return errorResponse("You must be logged in to remove favorites", 401);
    }

    await db
      .delete(favorites)
      .where(
        and(eq(favorites.userId, user.id), eq(favorites.productId, productId))
      );

    return successResponse(null, "Product removed from favorites", 200);
  } catch (error) {
    console.error("Error removing from favorites:", error);
    return errorResponse("Failed to remove product from favorites", 500);
  }
}

/**
 * Get all favorites for the current user
 */
export async function getUserFavorites() {
  try {
    const user = await isAuthenticated();

    if (!user) {
      return errorResponse("You must be logged in to view favorites", 401);
    }

    const userFavorites = await db
      .select({
        productId: favorites.productId,
      })
      .from(favorites)
      .where(eq(favorites.userId, user.id));

    return successResponse(
      { favorites: userFavorites.map((fav) => fav.productId) },
      "Favorites retrieved successfully",
      200
    );
  } catch (error) {
    console.error("Error getting favorites:", error);
    return errorResponse("Failed to get favorites", 500);
  }
}

/**
 * Get all favorite products with details for the current user
 */
export async function getUserFavoriteProducts() {
  try {
    const user = await isAuthenticated();

    if (!user) {
      return errorResponse("You must be logged in to view favorites", 401);
    }

    const favoriteProducts = await db
      .select({
        id: products.id,
        name: products.name,
        price: products.price,
        priceOld: products.priceOld,
        featuredImage: products.featuredImage,
        productImage: products.productImage,
        description: products.description,
      })
      .from(favorites)
      .innerJoin(products, eq(favorites.productId, products.id))
      .where(eq(favorites.userId, user.id));

    return successResponse(
      { products: favoriteProducts },
      "Favorite products retrieved successfully",
      200
    );
  } catch (error) {
    console.error("Error getting favorite products:", error);
    return errorResponse("Failed to get favorite products", 500);
  }
}

/**
 * Check if a product is in the user's favorites
 */
export async function isProductFavorited(productId: number) {
  try {
    const user = await isAuthenticated();

    if (!user) {
      return { isFavorited: false };
    }

    const existingFavorite = await db
      .select()
      .from(favorites)
      .where(
        and(eq(favorites.userId, user.id), eq(favorites.productId, productId))
      );

    return { isFavorited: existingFavorite.length > 0 };
  } catch (error) {
    console.error("Error checking favorite status:", error);
    return { isFavorited: false };
  }
}

/**
 * Toggle favorite status for a product
 */
export async function toggleFavorite(productId: number) {
  try {
    const user = await isAuthenticated();

    if (!user) {
      return errorResponse("You must be logged in to manage favorites", 401);
    }

    const existingFavorite = await db
      .select()
      .from(favorites)
      .where(
        and(eq(favorites.userId, user.id), eq(favorites.productId, productId))
      );

    if (existingFavorite.length > 0) {
      // Remove from favorites
      await db
        .delete(favorites)
        .where(
          and(eq(favorites.userId, user.id), eq(favorites.productId, productId))
        );
      return successResponse(
        { isFavorited: false },
        "Product removed from favorites",
        200
      );
    } else {
      // Add to favorites
      await db.insert(favorites).values({
        userId: user.id,
        productId: productId,
      });
      return successResponse(
        { isFavorited: true },
        "Product added to favorites",
        201
      );
    }
  } catch (error) {
    console.error("Error toggling favorite:", error);
    return errorResponse("Failed to update favorite status", 500);
  }
}
