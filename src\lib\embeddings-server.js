function generateHashEmbedding(text, dimensions = 384) {
  const embedding = new Array(dimensions).fill(0);

  // Simple hash-based approach
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    const index = char % dimensions;
    embedding[index] += Math.sin(char * 0.1) * 0.1;
  }

  // Normalize the embedding
  const magnitude = Math.sqrt(
    embedding.reduce((sum, val) => sum + val * val, 0)
  );
  if (magnitude > 0) {
    for (let i = 0; i < embedding.length; i++) {
      embedding[i] /= magnitude;
    }
  }

  return embedding;
}

function generateCompactEmbedding(text, dimensions = 128) {
  return generateHashEmbedding(text, dimensions);
}

/**
 * Reduce embedding dimensions while preserving relative relationships
 */
export function reduceEmbeddingDimensions(embedding, targetDimensions = 128) {
  if (embedding.length <= targetDimensions) {
    return embedding;
  }

  const ratio = embedding.length / targetDimensions;
  const reduced = new Array(targetDimensions).fill(0);

  for (let i = 0; i < targetDimensions; i++) {
    const startIdx = Math.floor(i * ratio);
    const endIdx = Math.floor((i + 1) * ratio);
    let sum = 0;
    let count = 0;

    for (let j = startIdx; j < endIdx && j < embedding.length; j++) {
      sum += embedding[j];
      count++;
    }

    reduced[i] = count > 0 ? sum / count : 0;
  }

  return reduced;
}

export async function generateEmbedding(text) {
  try {
    if (!text || typeof text !== "string" || text.trim() === "") {
      console.warn("Invalid or empty text provided for embedding");
      return generateHashEmbedding("default", 128); // Use compact fallback
    }

    // Try to use transformers first
    try {
      // Only try transformers in Node.js environment
      if (
        typeof process !== "undefined" &&
        process.versions &&
        process.versions.node
      ) {
        const { pipeline } = await import("@xenova/transformers");

        const embeddingPipeline = await pipeline(
          "feature-extraction",
          "Xenova/all-MiniLM-L6-v2",
          {
            device: "cpu",
            dtype: "fp32",
          }
        );

        const output = await embeddingPipeline(text, {
          pooling: "mean",
          normalize: true,
        });

        if (!output || !output.data) {
          throw new Error("Invalid transformer output");
        }

        const embeddings = Array.from(output.data);

        if (!Array.isArray(embeddings) || embeddings.length === 0) {
          throw new Error("Invalid embeddings array");
        }

        console.log(
          `Generated transformer embedding for text: "${text.substring(
            0,
            50
          )}..." (${embeddings.length} dimensions)`
        );
        return embeddings;
      }
    } catch (transformerError) {
      console.warn(
        "Transformer embedding failed, using fallback:",
        transformerError.message
      );
    }

    // Fallback to hash-based embedding (use compact 128 dimensions)
    console.log(
      `Using hash-based embedding fallback for: "${text.substring(0, 50)}..."`
    );
    return generateHashEmbedding(text, 128);
  } catch (error) {
    console.error("Error generating embedding:", error);
    // Return a compact zero embedding as last resort
    return new Array(128).fill(0);
  }
}

/**
 * Generate embeddings for product data
 * @param {Object} product - Product object with name, description, etc.
 * @returns {Promise<number[]>} - Array of embedding values
 */
export async function generateProductEmbedding(product) {
  try {
    // Combine relevant text fields for embedding
    const textParts = [];

    if (product.name) textParts.push(product.name);
    if (product.description) textParts.push(product.description);
    if (product.standardizedTitle) textParts.push(product.standardizedTitle);
    if (product.keywords && Array.isArray(product.keywords)) {
      textParts.push(product.keywords.join(" "));
    }
    if (product.category_name) textParts.push(product.category_name);

    const combinedText = textParts.join(" ").trim();

    if (!combinedText) {
      console.warn(
        "No text content found for product embedding, using zero embedding"
      );
      return new Array(128).fill(0); // Use compact dimensions
    }

    return await generateEmbedding(combinedText);
  } catch (error) {
    console.error("Error generating product embedding:", error);
    return new Array(128).fill(0); // Use compact dimensions
  }
}

/**
 * Batch generate embeddings for multiple products
 * @param {Array} products - Array of product objects
 * @param {number} batchSize - Number of products to process at once
 * @returns {Promise<Array>} - Array of products with embeddings
 */
export async function generateProductEmbeddingsBatch(products, batchSize = 5) {
  const results = [];

  console.log(
    `Generating embeddings for ${products.length} products in batches of ${batchSize}`
  );

  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    console.log(
      `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
        products.length / batchSize
      )}`
    );

    const batchPromises = batch.map(async (product) => {
      try {
        const embedding = await generateProductEmbedding(product);
        return {
          ...product,
          embedding,
        };
      } catch (error) {
        console.error(
          `Failed to generate embedding for product ${product.id}:`,
          error
        );
        return {
          ...product,
          embedding: new Array(128).fill(0), // Zero embedding as fallback (compact)
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Small delay between batches to avoid overwhelming the system
    if (i + batchSize < products.length) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  console.log(`Completed embedding generation for ${results.length} products`);
  return results;
}

/**
 * Check if transformers is available
 * @returns {Promise<boolean>}
 */
export async function isTransformersAvailable() {
  try {
    await import("@xenova/transformers");
    return true;
  } catch (error) {
    return false;
  }
}
