import React, { useState } from "react";
import Image from "next/image";
import filters from "../../assets/images/filters.png";
import buyoption from "../../assets/images/buyoption.png";
import truck from "../../assets/images/truck.png";
import returnimg from "../../assets/images/return.png";
import CustomButton from "../../components/button";
import FiltersDropdown from "../../components/Dropdown/FiltersDropdown";
import FiltersModal from "../../components/filtersModal";
import OneBuyModal from "../../components/oneBuyModal";
import { renderStars } from "@/app/utils/renderStars";
import { GoInfo } from "react-icons/go";
const BuyOptions = () => {
  const [filtersModal, setFiltersModal] = useState(false);
  const [oneBuyModal, setOneBuyModal] = useState(false);

  const buyOptionsList = Array.from({ length: 6 });
  return (
    <>
      {filtersModal && <FiltersModal setFiltersModal={setFiltersModal} />}
      {oneBuyModal && <OneBuyModal setOneBuyModal={setOneBuyModal} />}
      <div className="bg-[#F8F7F1] py-4 px-4 mt-8">
        <div className="flex justify-between">
          <div className="flex flex-row gap-5">
            <div className="w-16 h-16 my-auto flex items-center justify-center rounded-full bg-white">
              <Image
                src={filters}
                alt="filters"
                className="w-6 h-6 cursor-pointer"
                onClick={() => setFiltersModal(!filtersModal)}
              />
            </div>
            <div className="my-auto">
              <CustomButton
                label="Only in Stock"
                bgColor="bg-white"
                textColor="text-black"
                paddingx="px-4"
                paddingY="py-4"
                borderCustom="rounded-full"
                textSize="text-md"
              />
            </div>
            <div className="my-auto">
              <CustomButton
                label="Price incl. delivery"
                bgColor="bg-white"
                textColor="text-black"
                paddingx="px-4"
                paddingY="py-4"
                borderCustom="rounded-full"
                textSize="text-md"
              />
            </div>
            <div className="my-auto">
              <FiltersDropdown
                padding="p-4"
                rounded="rounded-full"
                options={[
                  "Show all",
                  "Home delivery",
                  "Service point",
                  "Store pickup",
                ]}
              />
            </div>
          </div>
          <div className="my-auto">
            <FiltersDropdown
              padding="p-4"
              rounded="rounded-full"
              options={["Fastest Delivery", "Lowest Price", "Recommended"]}
              link=" How we rank products"
            />
          </div>
        </div>
        {buyOptionsList.map((item) => (
          <div
            key={item}
            className="grid grid-cols-12 gap-4 Sora bg-white p-5 mt-5 rounded-xl items-center"
          >
            {/* Logo + Rating */}
            <div className="col-span-2 flex flex-col items-start gap-2">
              <Image src={buyoption} height={40} alt="Seller logo" />
              <div className="flex items-center text-sm text-gray-600">
                <div className="flex mr-2">{renderStars(3.5)}</div>
                <span className="mr-1">{3.5}</span>
                <span className="text-gray-500">(2,101)</span>
              </div>
            </div>

            {/* Delivery Info */}
            <div className="col-span-4 space-y-2 mx-auto">
              <div className="flex items-center gap-3 text-sm text-black">
                <Image
                  src={truck}
                  className="w-6 h-6 object-contain"
                  alt="Truck"
                />
                <span>Delivery time: 1–3 working days</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-black">
                <Image
                  src={returnimg}
                  className="w-5 h-5 object-contain"
                  alt="Return"
                />
                <span>30 days free return</span>
              </div>
            </div>

            {/* Store Info */}
            <div className="col-span-2 flex items-center gap-2 mx-auto text-red-500 text-sm">
              <GoInfo className="text-red-500" />
              <span className="underline cursor-pointer">Store Info</span>
            </div>

            {/* Price */}
            <div className="col-span-2 text-right space-y-1">
              <p className="transformaSansBold text-[#ED69EF] text-lg">
                519,00 $
              </p>
              <p className="text-black text-sm">$5 Shipping</p>
              <p className="text-black font-semibold text-sm">Total 524,00$</p>
            </div>

            {/* Button */}
            <div className="col-span-2 flex justify-end">
              <button
                className="bg-red-500 cursor-pointer text-white px-5 py-2 rounded-full text-sm font-medium"
                onClick={() => setOneBuyModal(!oneBuyModal)}
              >
                Buy Now
              </button>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default BuyOptions;
