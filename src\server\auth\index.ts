"use server";

// Imports
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import bcrypt from "bcrypt";
import { handleError } from "@/lib/common/error-handler";
import { successResponse, errorResponse } from "@/lib/common/responses";
import jwt from "jsonwebtoken";
import { setAuthCookie } from "@/lib/cookies";
const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret_here";
const JWT_EXPIRATION = "1d";

// Define APIError type
interface APIError extends Error {
  status?: number;
}

// Register User function
export async function registerUser(
  firstName: string,
  lastName: string,
  email: string,
  password: string
) {
  try {
    console.log(firstName, lastName, email, password, "data of user");
    // Input validation
    if (!firstName || !lastName || !email || !password) {
      return errorResponse("All fields are required", 400);
    }

    // Check for existing user
    const existingUser = await db.query.users.findFirst({
      where: (user, { eq }) => eq(user.email, email),
    });

    if (existingUser) {
      console.log("User with this email already exists");
      return errorResponse("User with this email already exists", 409);
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    await db.insert(users).values({
      firstName: firstName,
      lastName: lastName,
      email: email,
      password: hashedPassword,
      role: "customer",
      isActive: true,
      loginMethod: "manual", // Track that this user signed up manually
    });

    return successResponse<null>(null, "User registered successfully", 201);
  } catch (error) {
    return handleError(error as APIError);
  }
}

export async function loginUser(email: string, password: string) {
  try {
    if (!email || !password) {
      return errorResponse("Email and password are required", 400);
    }

    const user = await db.query.users.findFirst({
      where: (u, { eq }) => eq(u.email, email),
    });

    if (!user) {
      return errorResponse("Invalid email or password", 401);
    }

    if (!user.isActive) {
      return errorResponse("Your account is inactive", 403);
    }

    // Check if the user is trying to log in with a password when they signed up with Google
    if (user.loginMethod === "google") {
      return errorResponse("Please use Google to sign in to this account", 401);
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return errorResponse("Invalid email or password", 401);
    }

    const userData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
    };

    const token = jwt.sign(userData, JWT_SECRET, { expiresIn: JWT_EXPIRATION });
    await setAuthCookie(token);
    return successResponse({ user: userData }, "Login successful", 200);
  } catch (error) {
    return handleError(error as APIError);
  }
}
