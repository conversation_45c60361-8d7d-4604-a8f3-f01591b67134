import Link from "next/link";
import { useState } from "react";
import { IoIosArrowDown } from "react-icons/io";

export default function FiltersDropdown({
  backgroundColor = null,
  rounded = null,
  padding = null,
  textSize = null,
  textColor = null,
  options = null,
  link = null,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelected] = useState("Show all");

  return (
    <div className="relative inline-block text-left">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
            ${backgroundColor ? backgroundColor : "bg-white"}
            ${rounded ? rounded : "rounded-lg"}
            ${padding ? padding : "px-4 py-2"}
            ${textSize ? textSize : "text-md"}
            ${textColor ? textColor : "text-black"} Sora cursor-pointer
            `}
      >
        <div className="flex flex-row gap-5">
          Delivery Options
          <IoIosArrowDown className="my-auto" />
        </div>
      </button>

      {isOpen && (
        <div className="absolute mt-2 w-48 rounded-xl bg-white Sora text-black shadow-xl z-50">
          <div className="p-2 space-y-1">
            {options?.map((option, idx) => (
              <label
                key={idx}
                className="flex items-center gap-2 px-2 py-1.5 cursor-pointer text-sm hover:bg-gray-50 rounded-md"
              >
                <input
                  type="radio"
                  name="delivery"
                  checked={selected === option}
                  onChange={() => setSelected(option)}
                  className="appearance-none w-4 h-4 rounded-full border border-purple-600 checked:bg-purple-600 checked:ring-4 checked:ring-purple-300 checked:ring-opacity-40"
                />
                {option}
              </label>
            ))}
            {link && (
              <Link href="#" className="text-sm text-gray-500 underline Sora">
                {link}
              </Link>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
