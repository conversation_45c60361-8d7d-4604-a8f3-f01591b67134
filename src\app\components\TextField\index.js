import React from "react";

const TextField = ({
  customWidth = null,
  customPadding = null,
  customBorder = null,
  customPlaceholder,
  Icon = null,
  iconColor = null,
  iconBackground = null,
  iconDimensions = null,
  iconPadding = null,
  type = null,
  name = null,
  id = null,
  passwordIcon,
  isPasswordVisible,
  setIsPasswordVisible,
  value = null,
  onChange = null,
  onBlur = null,
}) => {
  return (
    <div className="relative">
      <input
        type={type ? type : "text"}
        name={name ? name : "price"}
        id={id ? id : "price"}
        className={`block w-full Sora ${customWidth ? customWidth : "w-auto"} 
        ${customPadding ? customPadding : "py-1.5 pr-3 pl-1 "}
        ${customBorder ? customBorder : "border border-gray-300"}
        grow text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6`}
        placeholder={customPlaceholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
      />
      {Icon && (
        <div className="absolute inset-y-0 right-0 flex items-center px-4 text-gray-600 cursor-pointer">
          <Icon
            className={`
              ${iconColor && iconColor}
            ${iconBackground && iconBackground}
            ${iconDimensions && iconDimensions}
            ${iconPadding && iconPadding}
               rounded-full`}
          />
        </div>
      )}
      {passwordIcon && (
        <div
          className="absolute inset-y-0 right-0 flex items-center px-4 text-gray-600 cursor-pointer"
          onClick={() => {
            setIsPasswordVisible(!isPasswordVisible);
          }}
        >
          {isPasswordVisible ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          )}
        </div>
      )}
    </div>
  );
};

export default TextField;
