import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "../app/components/navbar/navbar";
import Footer from "./components/footer";
import ReduxProvider from "@/redux/ReduxProvider";
import QueryProvider from "./providers/QueryProvider";
import SchemaOrgServer from "./components/SchemaOrg/server";
import {
  ClientToastContainer,
  ClientGoogleAuthHandler,
  ClientAuthLoadingOverlay,
} from "./components/ClientComponents";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Pricio - Smart Price Comparison",
  description:
    "AI-powered price comparison to help you find the best deals worldwide.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Organization schema data
  const organizationSchema = {
    name: "<PERSON><PERSON><PERSON>",
    url: "https://pricio.com",
    logo: "https://pricio.com/logo.png", // Update with your actual logo URL
    sameAs: [
      "https://facebook.com/pricio",
      "https://twitter.com/pricio",
      "https://instagram.com/pricio",
    ],
  };

  // Website schema data
  const websiteSchema = {
    name: "Pricio - Smart Price Comparison",
    url: "https://pricio.com",
    potentialAction: {
      target: "https://pricio.com/search?q={search_term_string}",
      queryInput: "required name=search_term_string",
    },
  };

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SchemaOrgServer type="Organization" data={organizationSchema} />
        <SchemaOrgServer type="WebSite" data={websiteSchema} />

        <ReduxProvider>
          <QueryProvider>
            <ClientAuthLoadingOverlay />
            <ClientToastContainer />
            <ClientGoogleAuthHandler />
            <Navbar />
            {children}
            <Footer />
          </QueryProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
