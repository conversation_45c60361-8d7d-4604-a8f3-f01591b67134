import {
  FaFacebookF,
  FaLinkedinIn,
  FaYoutube,
  FaInstagram,
  FaTiktok,
  Fa<PERSON><PERSON><PERSON>,
} from "react-icons/fa";

const Footer = () => {
  return (
    <footer className="bg-black text-white py-10">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center md:items-start">
        {/* Left Section */}
        <div className="md:w-2/3 text-center md:text-left">
          <h2 className="text-2xl font-bold mb-4 transformaScriptSemibold">
            Pricio
          </h2>
          <p className="text-gray-300 text-sm leading-relaxed">
            Lorem Ipsum is simply dummy text of the printing and typesetting
            industry. Lorem Ipsum has been the industry&apos;s standard dummy
            text ever since the 1500s, when an unknown printer took a galley of
            type and scrambled it to make a type specimen book. It has survived
            not only five centuries.
          </p>
          <p className="text-gray-300 text-xs mt-4">
            © 2025 All rights reserved. Pricio.
          </p>
        </div>

        {/* Right Section - Social Icons */}
        <div className="flex space-x-4 mt-6 md:mt-14">
          <FaFacebookF className="text-white hover:text-gray-400 cursor-pointer" />
          <FaTwitter className="text-white hover:text-gray-400 cursor-pointer" />
          <FaLinkedinIn className="text-white hover:text-gray-400 cursor-pointer" />
          <FaYoutube className="text-white hover:text-gray-400 cursor-pointer" />
          <FaInstagram className="text-white hover:text-gray-400 cursor-pointer" />
          <FaTiktok className="text-white hover:text-gray-400 cursor-pointer" />
        </div>
      </div>
    </footer>
  );
};

export default Footer;
