"use server";

import { getAuth<PERSON>ookie } from "./cookies";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret_here";

interface DecodedToken {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Check if the user is authenticated by verifying the JWT token in the cookie
 * @returns User data if authenticated, null otherwise
 */
export async function isAuthenticated() {
  try {
    const token = await getAuthCookie();

    if (!token) {
      return null;
    }
    const decoded = jwt.verify(token, JWT_SECRET) as DecodedToken;
    return {
      id: decoded.id,
      firstName: decoded.firstName,
      lastName: decoded.lastName,
      email: decoded.email,
      role: decoded.role,
    };
  } catch (error) {
    console.log(error);
    return null;
  }
}

export async function logout() {
  // This will be implemented in a separate file for client-side usage
}
