import { NextResponse } from "next/server";
import { syncProductsToTypesense } from "@/server/typesense/sync";

export async function GET() {
  try {
    console.log("API route: Starting Typesense sync...");
    const result = await syncProductsToTypesense();
    console.log("API route: Sync result:", result);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("API route: Sync error:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to sync products to Typesense", 
        error: error.message 
      },
      { status: 500 }
    );
  }
}
