"use server";

import { db } from "@/lib/db";
import { categories } from "@/lib/schema";
export const getCategories = async (): Promise<CategoryNode[]> => {
  try {
    const allCategories: Category[] = await db.select().from(categories);

    const buildCategoryTree = (parentId: number | null): CategoryNode[] => {
      return allCategories
        .filter((cat) => cat.parentId === parentId)
        .map((cat) => ({
          ...cat,
          subcategories: buildCategoryTree(cat.id),
        }));
    };

    return buildCategoryTree(null);
  } catch (error) {
    console.error("Error fetching categories with subcategories:", error);
    throw new Error("Could not fetch categories");
  }
};

// interface CategoryNode {
//   name: string;
//   slug: string;
//   children?: CategoryNode[];
// }

// interface CategoryTreeResponse {
//   tree: CategoryNode;
//   selectedSlug: string;
//   selectedPath: string[];
// }

// export const getSubcategoriesByParentSlug = async (
//   selectedSlug: string
// ): Promise<CategoryTreeResponse> => {
//   const allCategories = await db.select().from(categories);

//   const categoryMap = new Map<number, any>();
//   const slugMap = new Map<string, any>();

//   allCategories.forEach((cat) => {
//     categoryMap.set(cat.id, { ...cat, children: [] });
//     slugMap.set(cat.slug, cat);
//   });

//   const roots: any[] = [];
//   allCategories.forEach((cat) => {
//     const node = categoryMap.get(cat.id);
//     if (cat.parentId) {
//       const parent = categoryMap.get(cat.parentId);
//       parent.children.push(node);
//     } else {
//       roots.push(node);
//     }
//   });

//   const selected = slugMap.get(selectedSlug);
//   if (!selected) throw new Error("Selected category not found");

//   const selectedPath: string[] = [];
//   let current = selected;
//   let rootNode = null;
//   while (current) {
//     selectedPath.unshift(current.slug);
//     if (!current.parentId) {
//       rootNode = categoryMap.get(current.id);
//     }
//     current = current.parentId ? categoryMap.get(current.parentId) : null;
//   }

//   const cleanTree = (node: any): CategoryNode => ({
//     name: node.name,
//     slug: node.slug,
//     children: node.children.length ? node.children.map(cleanTree) : undefined,
//   });

//   return {
//     tree: cleanTree(rootNode),
//     selectedSlug,
//     selectedPath,
//   };
// };

type Category = {
  id: number;
  name: string;
  slug: string;
  parentId: number | null;
};

type CategoryNode = {
  name: string;
  slug: string;
  children?: CategoryNode[];
};

type CategoryTreeResponse = {
  tree: CategoryNode;
  selectedSlug: string;
  selectedPath: string[];
};

export const getSubcategoriesByParentSlug = async (
  selectedSlug: string
): Promise<CategoryTreeResponse> => {
  const allCategories: Category[] = await db.select().from(categories);

  type CategoryWithChildren = Category & { children: CategoryWithChildren[] };

  const categoryMap = new Map<number, CategoryWithChildren>();
  const slugMap = new Map<string, Category>();

  allCategories.forEach((cat) => {
    categoryMap.set(cat.id, { ...cat, children: [] });
    slugMap.set(cat.slug, cat);
  });

  const roots: CategoryWithChildren[] = [];

  allCategories.forEach((cat) => {
    const node = categoryMap.get(cat.id)!;
    if (cat.parentId !== null) {
      const parent = categoryMap.get(cat.parentId);
      if (parent) {
        parent.children.push(node);
      }
    } else {
      roots.push(node);
    }
  });

  const selected = slugMap.get(selectedSlug);
  if (!selected) throw new Error("Selected category not found");

  const selectedPath: string[] = [];
  let current: Category | undefined = selected;
  let rootNode: CategoryWithChildren | null = null;

  while (current) {
    selectedPath.unshift(current.slug);
    if (current.parentId === null) {
      rootNode = categoryMap.get(current.id)!;
    }
    current =
      current.parentId !== null ? categoryMap.get(current.parentId) : undefined;
  }

  if (!rootNode) throw new Error("Root node not found");

  const cleanTree = (node: CategoryWithChildren): CategoryNode => ({
    name: node.name,
    slug: node.slug,
    children: node.children.length ? node.children.map(cleanTree) : undefined,
  });

  return {
    tree: cleanTree(rootNode),
    selectedSlug,
    selectedPath,
  };
};
