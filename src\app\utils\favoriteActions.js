"use client";

import { toggleFavorite as toggleFavoriteServer } from "@/server/favorites";
import { toast } from "react-toastify";
import { FAVORITES_QUERY_KEY } from "@/app/hooks/useFavorites";

export const toggleProductFavorite = async (productId, setFavorites = null) => {
  try {
    const response = await toggleFavoriteServer(productId);

    if (response.success) {
      if (setFavorites) {
        setFavorites((prev) => {
          const newFavorites = new Set(prev);
          if (response.data.isFavorited) {
            newFavorites.add(productId);
          } else {
            newFavorites.delete(productId);
          }
          return newFavorites;
        });
      }

      // Show success message
      toast.success(response.message, {
        autoClose: 2000,
      });

      return response.data.isFavorited;
    } else {
      // Handle error
      if (response.statusCode === 401) {
        // User is not logged in
        toast.error("Please log in to save favorites");
      } else {
        toast.error(response.message || "Failed to update favorites");
      }
      return false;
    }
  } catch (error) {
    console.error("Error toggling favorite:", error);
    toast.error("Something went wrong. Please try again.", {
      position: "bottom-right",
    });
    return false;
  }
};
