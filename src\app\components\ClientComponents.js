"use client";

import { useState, useEffect, Suspense } from "react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import GoogleAuthHandler from "./GoogleAuthHandler";
import LoadingOverlay from "./LoadingOverlay";
import { useSearchParams } from "next/navigation";

export function ClientToastContainer() {
  return <ToastContainer position="top-center" autoClose={5000} />;
}

// Wrap GoogleAuthHandler in a Suspense boundary
export function ClientGoogleAuthHandler() {
  return (
    <Suspense fallback={null}>
      <GoogleAuthHandlerWithSuspense />
    </Suspense>
  );
}

function GoogleAuthHandlerWithSuspense() {
  return <GoogleAuthHandler />;
}

// Wrap LoadingOverlay in a Suspense boundary
export function ClientAuthLoadingOverlay() {
  return (
    <Suspense fallback={null}>
      <AuthLoadingOverlayWithSuspense />
    </Suspense>
  );
}

function AuthLoadingOverlayWithSuspense() {
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();

  useEffect(() => {
    const googleLoginSuccess = searchParams.get("googleLoginSuccess");
    const error = searchParams.get("error");
    if (googleLoginSuccess === "true" || error) {
      setIsLoading(true);
      if (error) {
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      }
    }
  }, [searchParams]);

  return <LoadingOverlay isVisible={isLoading} message="Logging you in..." />;
}
