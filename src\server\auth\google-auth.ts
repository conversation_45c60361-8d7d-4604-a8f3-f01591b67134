"use server";

import { redirect } from "next/navigation";

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const REDIRECT_URI = `${
  process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
}/api/auth/google/callback`;

export async function initiateGoogleLogin() {
  if (!GOOGLE_CLIENT_ID) {
    throw new Error("Google OAuth is not configured");
  }

  const params = new URLSearchParams({
    client_id: GOOGLE_CLIENT_ID,
    redirect_uri: REDIRECT_URI,
    response_type: "code",
    scope: "openid email profile",
    access_type: "offline",
    prompt: "consent",
  });

  const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;

  // Redirect to Google's authentication page
  redirect(googleAuthUrl);
}
