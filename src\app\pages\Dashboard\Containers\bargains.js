"use client";
import Image from "next/image";
import React from "react";
import stars from "../../../assets/images/stars.png";
import PopularProducts from "@/app/components/popular";
// import { useRouter } from "next/navigation";

const CurrentBargains = () => {
  // const router = useRouter();

  const handleNavigation = () => {
    // router.push(`/categories`);
  };

  return (
    <div className="container mx-auto px-4 mt-10">
      <div className="flex flex-row mb-6 gap-4">
        <h2 className="text-2xl font-bold transformaSansSemibold text-black  text-left">
          Current bargains for you
        </h2>
        <Image
          src={stars}
          alt="stars"
          className="object-cover"
          height={20}
          width={20}
        />
      </div>
      <PopularProducts slidePercentage={20} currentSlideNumber={1} />
      <div className="mx-auto flex justify-center">
        <button
          className="mt-8 px-3 py-2 text-black cursor-pointer border-1 border-black rounded-full text-sm transition hover:bg-black hover:text-white"
          onClick={() => handleNavigation()}
        >
          Show all bargains
        </button>
      </div>
    </div>
  );
};

export default CurrentBargains;
