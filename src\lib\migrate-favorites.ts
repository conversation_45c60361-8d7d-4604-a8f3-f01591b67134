import { db } from "./db";
import { sql } from "drizzle-orm";
import dotenv from "dotenv";

dotenv.config();

const main = async () => {
  try {
    // Check if favorite table exists
    const checkTableResult = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'favorite'
      );
    `);

    const tableExists = checkTableResult.rows[0].exists;

    if (!tableExists) {
      console.log("Creating 'favorite' table...");
      await db.execute(sql`
        CREATE TABLE "favorite" (
          "userId" integer NOT NULL REFERENCES "user"("id") ON DELETE CASCADE,
          "productId" integer NOT NULL REFERENCES "product"("id") ON DELETE CASCADE,
          "createdAt" timestamp DEFAULT NOW() NOT NULL,
          PRIMARY KEY ("userId", "productId")
        );
      `);
      console.log("✅ Successfully created 'favorite' table!");
    } else {
      console.log("'favorite' table already exists.");
    }

    console.log("✅ Favorites migration completed successfully!");
  } catch (error) {
    console.error("❌ Error during favorites migration:", error);
    process.exit(1);
  }
};

main();
