#!/usr/bin/env node

require("dotenv").config();
const Typesense = require("typesense");
const { Pool } = require("pg");

// Create Typesense client
const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: "localhost",
      port: "8109",
      protocol: "http",
    },
  ],
  apiKey: "xyz",
  connectionTimeoutSeconds: 2,
});

// Connect to PostgreSQL
const pool = new Pool({
  connectionString:
    process.env.DATABASE_URL ||
    "**********************************************************",
});

// Define the schema for the products collection
const productsCollectionSchema = {
  name: "products",
  fields: [
    { name: "id", type: "int32" },
    { name: "name", type: "string" },
    { name: "description", type: "string", optional: true },
    { name: "price", type: "float" },
    { name: "priceOld", type: "float", optional: true },
    { name: "featuredImage", type: "string", optional: true },
    { name: "keywords", type: "string[]", optional: true },
    { name: "standardizedTitle", type: "string", optional: true },
    { name: "category_id", type: "string", optional: true },
    { name: "category_name", type: "string", optional: true },
    { name: "category_slug", type: "string", optional: true },
    { name: "embedding", type: "float[]", optional: true, num_dim: 384 }, // Vector field for embeddings
  ],
  default_sorting_field: "price",
};

async function syncProductsToTypesense() {
  try {
    console.log("Checking if Typesense server is reachable...");
    try {
      // First check if we can reach the Typesense server at all
      const health = await typesenseClient.health.retrieve();
      console.log("Typesense server is reachable:", health);

      console.log("Checking if collection exists...");
      // Check if collection exists, if not create it
      try {
        await typesenseClient.collections("products").retrieve();
        console.log("Collection already exists");
      } catch (error) {
        console.log("Collection retrieval error:", error.message);

        // Check if it's a 404 error (collection not found)
        if (
          error.message.includes("404") ||
          error.message.includes("Not Found")
        ) {
          console.log("Collection does not exist, creating it now...");
          try {
            const result = await typesenseClient
              .collections()
              .create(productsCollectionSchema);
            console.log("Created new collection: products", result);
          } catch (createError) {
            console.error("Error creating collection:", createError);
            throw createError;
          }
        } else {
          console.error("Error retrieving collection:", error);
          throw error;
        }
      }
    } catch (error) {
      console.error("Error connecting to Typesense:", error.message);
      throw error;
    }

    console.log("Fetching products with categories from PostgreSQL...");
    console.log(
      "Database connection string:",
      process.env.DATABASE_URL
        ? "Using environment variable"
        : "Using fallback connection string"
    );

    try {
      console.log("Testing database connection...");
      await pool.query("SELECT 1");
      console.log("Database connection successful");

      // Check table names
      console.log("Checking table names...");
      const { rows: tables } = await pool.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
      `);
      console.log(
        "Available tables:",
        tables.map((t) => t.table_name).join(", ")
      );

      // Check product table
      console.log("Checking product table structure...");
      const { rows: productColumns } = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'product'
      `);
      console.log(
        "Product table columns:",
        productColumns
          .map((c) => `${c.column_name} (${c.data_type})`)
          .join(", ")
      );

      // Check category table
      console.log("Checking category table structure...");
      const { rows: categoryColumns } = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'category'
      `);
      console.log(
        "Category table columns:",
        categoryColumns
          .map((c) => `${c.column_name} (${c.data_type})`)
          .join(", ")
      );

      // Check join table
      console.log("Checking product_categories_category table structure...");
      const { rows: joinColumns } = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'product_categories_category'
      `);
      console.log(
        "Join table columns:",
        joinColumns.map((c) => `${c.column_name} (${c.data_type})`).join(", ")
      );
    } catch (dbError) {
      console.error("Database connection test failed:", dbError);
      throw new Error(`Database connection failed: ${dbError.message}`);
    }

    console.log("Executing query to fetch products with categories...");
    // Get all products with their categories
    const query = `
      SELECT
        p.id,
        p.name,
        p.description,
        p.price,
        p."priceOld",
        p."featuredImage",
        p.keywords,
        p."standardizedTitle",
        c.id as category_id,
        c.name as category_name,
        c.slug as category_slug
      FROM product p
      INNER JOIN product_categories_category pcc ON p.id = pcc."productId"
      INNER JOIN category c ON pcc."categoryId" = c.id
      LIMIT 10
    `;

    console.log("Query:", query);

    let productsWithCategories;
    try {
      const { rows: testProducts } = await pool.query(query);
      console.log(
        `Query executed successfully, found ${testProducts.length} products`
      );

      // If we got results with the limit, remove the limit and get all products
      if (testProducts.length > 0) {
        console.log("Test query successful, now fetching all products...");
        const fullQuery = `
          SELECT
            p.id,
            p.name,
            p.description,
            p.price,
            p."priceOld",
            p."featuredImage",
            p.keywords,
            p."standardizedTitle",
            c.id as category_id,
            c.name as category_name,
            c.slug as category_slug
          FROM product p
          INNER JOIN product_categories_category pcc ON p.id = pcc."productId"
          INNER JOIN category c ON pcc."categoryId" = c.id
          LIMIT 10000
        `;
        const { rows: allProducts } = await pool.query(fullQuery);
        productsWithCategories = allProducts;
      } else {
        productsWithCategories = testProducts;
      }
    } catch (queryError) {
      console.error("Error executing query:", queryError);
      throw new Error(`Database query failed: ${queryError.message}`);
    }

    if (!productsWithCategories.length) {
      return { success: false, message: "No products found to sync" };
    }

    console.log(`Found ${productsWithCategories.length} products to sync`);

    // Format documents for Typesense
    const documents = productsWithCategories.map((product) => ({
      id: product.id.toString(),
      name: product.name || "",
      description: product.description || "",
      price: product.price || 0,
      priceOld: product.priceOld || 0,
      featuredImage: product.featuredImage || "",
      keywords: product.keywords || [],
      standardizedTitle: product.standardizedTitle || "",
      category_id: product.category_id ? product.category_id.toString() : "",
      category_name: product.category_name || "",
      category_slug: product.category_slug || "",
    }));

    console.log("Importing products to Typesense in batches...");

    // Import documents to Typesense in batches
    const BATCH_SIZE = 1000;
    let successCount = 0;
    let errorCount = 0;
    let errors = [];

    // Process in batches
    for (let i = 0; i < documents.length; i += BATCH_SIZE) {
      const batch = documents.slice(i, i + BATCH_SIZE);
      console.log(
        `Processing batch ${i / BATCH_SIZE + 1} of ${Math.ceil(
          documents.length / BATCH_SIZE
        )} (${batch.length} products)`
      );

      try {
        const batchResponse = await typesenseClient
          .collections("products")
          .documents()
          .import(batch, { action: "upsert" });

        // Count successes and errors
        const batchErrors = batchResponse.filter(
          (res) => res.success === false
        );
        errorCount += batchErrors.length;
        successCount += batch.length - batchErrors.length;

        if (batchErrors.length > 0) {
          errors = errors.concat(batchErrors);
          console.log(`  ⚠️ Batch had ${batchErrors.length} errors`);
        } else {
          console.log(`  ✅ Batch imported successfully`);
        }
      } catch (error) {
        console.error(`  ❌ Error importing batch:`, error.message);
        errorCount += batch.length;
        errors.push({
          message: `Failed to import batch starting at index ${i}`,
          error: error.message,
        });
      }
    }

    console.log(
      `Import summary: ${successCount} successes, ${errorCount} errors`
    );

    if (errorCount > 0) {
      console.error(`There were ${errorCount} errors during import`);
      // Only show the first 10 errors to avoid overwhelming the console
      if (errors.length > 10) {
        console.error("First 10 errors:", errors.slice(0, 10));
        console.error(`...and ${errors.length - 10} more errors`);
      } else {
        console.error("Errors:", errors);
      }

      return {
        success: false,
        message: `Some products failed to sync (${errorCount} errors)`,
        errors: errors.slice(0, 10), // Only return the first 10 errors to avoid response size issues
      };
    }

    return {
      success: true,
      message: `Successfully synced ${documents.length} products to Typesense`,
    };
  } catch (error) {
    console.error("Error syncing products to Typesense:", error);
    return {
      success: false,
      message: "Failed to sync products to Typesense",
      error: error.message,
    };
  } finally {
    // Close the database connection
    await pool.end();
  }
}

async function main() {
  console.log("Starting Typesense sync...");

  try {
    const result = await syncProductsToTypesense();

    if (result.success) {
      console.log("✅ " + result.message);
      process.exit(0);
    } else {
      console.error("❌ " + result.message);
      if (result.errors) {
        console.error("Errors:", result.errors);
      }
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ Sync failed with error:", error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error("Unhandled error:", error);
  process.exit(1);
});
