import Image from "next/image";
import CategoriesServer from "../../components/categories/CategoriesComponent";
import PopularContainer from "./Containers/popular";
import banner from "../../assets/images/banner.png";
import CurrentBargains from "./Containers/bargains";
import BestWeek1 from "./Containers/bestweek1";
import BestWeek2 from "./Containers/bestweek2";
import BestWeek3 from "./Containers/bestweek3";
import cta from "../../assets/images/cta.png";
import text from "../../assets/images/text.png";
import { ToastContainer, Zoom } from "react-toastify";

export default function DashboardServer() {
  return (
    <>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <div className="min-h-screen py-12 mt-[14rem]">
        <PopularContainer />
        <div className="container mx-auto px-4 mt-10">
          <Image src={banner} alt="banner" />
        </div>
        <CurrentBargains />
        <div className="bg-[#F8F7F1] container rounded-lg mx-auto py-10 mt-10">
          <h2 className="text-2xl font-bold px-4 mb-4 transformaSansSemibold text-black  text-left">
            Trending categories
          </h2>
          <CategoriesServer fixed="false" background="bg-transparent" />
        </div>
        <BestWeek1 />
        <BestWeek2 />
        <BestWeek3 />
        <div className="container mx-auto px-4 mt-10">
          <Image src={cta} alt="cta" />
        </div>
        <div className="px-4 container mx-auto mt-14">
          <div className="py-8 bg-[#15361B] rounded-xl">
            <div className="p-8">
              <Image src={text} height={180} alt="textLogo" />
              <div className="grid grid-cols-3 px-20 gap-20 text-center">
                <div className="flex flex-col">
                  <h1 className="text-2xl transformaSansBold text-[#E0F069]">
                    Smart Savings
                  </h1>
                  <p className="text-white">
                    AI-powered price comparison to help you find the best deals
                    worldwide.
                  </p>
                </div>
                <div className="flex flex-col">
                  <h1 className="text-2xl transformaSansBold text-[#E0F069]">
                    Limitless Choices
                  </h1>

                  <p className="text-white">
                    AI-powered price comparison to help you find the best deals
                    worldwide.
                  </p>
                </div>
                <div className="flex flex-col">
                  <h1 className="text-2xl transformaSansBold text-[#E0F069]">
                    Trusted & Secure
                  </h1>
                  <p className="text-white">
                    A reliable platform ensuring a seamless and safe shopping
                    experience.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
