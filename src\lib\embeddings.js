import { pipeline } from '@xenova/transformers';

// Initialize the embedding pipeline
let embeddingPipeline = null;

async function getEmbeddingPipeline() {
  if (!embeddingPipeline) {
    console.log('Loading embedding model...');
    // Using a lightweight, fast model for embeddings
    embeddingPipeline = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
    console.log('Embedding model loaded successfully');
  }
  return embeddingPipeline;
}

/**
 * Generate embeddings for a given text
 * @param {string} text - The text to generate embeddings for
 * @returns {Promise<number[]>} - Array of embedding values
 */
export async function generateEmbedding(text) {
  try {
    if (!text || typeof text !== 'string') {
      throw new Error('Text must be a non-empty string');
    }

    const pipeline = await getEmbeddingPipeline();
    
    // Generate embeddings
    const output = await pipeline(text, { pooling: 'mean', normalize: true });
    
    // Convert to regular array
    const embeddings = Array.from(output.data);
    
    console.log(`Generated embedding for text: "${text.substring(0, 50)}..." (${embeddings.length} dimensions)`);
    
    return embeddings;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * Generate embeddings for product data
 * @param {Object} product - Product object with name, description, etc.
 * @returns {Promise<number[]>} - Array of embedding values
 */
export async function generateProductEmbedding(product) {
  try {
    // Combine relevant text fields for embedding
    const textParts = [];
    
    if (product.name) textParts.push(product.name);
    if (product.description) textParts.push(product.description);
    if (product.standardizedTitle) textParts.push(product.standardizedTitle);
    if (product.keywords && Array.isArray(product.keywords)) {
      textParts.push(product.keywords.join(' '));
    }
    if (product.category_name) textParts.push(product.category_name);
    
    const combinedText = textParts.join(' ').trim();
    
    if (!combinedText) {
      throw new Error('No text content found for product embedding');
    }
    
    return await generateEmbedding(combinedText);
  } catch (error) {
    console.error('Error generating product embedding:', error);
    throw error;
  }
}

/**
 * Batch generate embeddings for multiple products
 * @param {Array} products - Array of product objects
 * @param {number} batchSize - Number of products to process at once
 * @returns {Promise<Array>} - Array of products with embeddings
 */
export async function generateProductEmbeddingsBatch(products, batchSize = 10) {
  const results = [];
  
  console.log(`Generating embeddings for ${products.length} products in batches of ${batchSize}`);
  
  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(products.length / batchSize)}`);
    
    const batchPromises = batch.map(async (product) => {
      try {
        const embedding = await generateProductEmbedding(product);
        return {
          ...product,
          embedding
        };
      } catch (error) {
        console.error(`Failed to generate embedding for product ${product.id}:`, error);
        return {
          ...product,
          embedding: null
        };
      }
    });
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Small delay between batches to avoid overwhelming the system
    if (i + batchSize < products.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  console.log(`Completed embedding generation for ${results.length} products`);
  return results;
}
