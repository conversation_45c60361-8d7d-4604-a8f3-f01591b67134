import React from "react";

const DropdownItem = ({
  onClick,
  children,
  icon,
  className = "",
  hoverEffect = "hover:bg-[#F0381A] hover:text-white",
}) => {
  return (
    <li
      className={`flex items-center gap-2 px-5 Sora py-2 text-sm font-medium text-coalColor cursor-pointer ${hoverEffect} ${className}`}
      onClick={onClick}
    >
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span className="flex-grow">{children}</span>
    </li>
  );
};

export default DropdownItem;
