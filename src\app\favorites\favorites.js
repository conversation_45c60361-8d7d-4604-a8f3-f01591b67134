"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>pin<PERSON> } from "react-icons/fa";
import { FaArrowLeft } from "react-icons/fa6";
import { renderStars } from "@/app/utils/renderStars";
import { toggleProductFavorite } from "@/app/utils/favoriteActions";
import { useFavorites } from "@/app/hooks/useFavorites";
import { getUserFavoriteProducts } from "@/server/favorites";
import { useRouter } from "next/navigation";
import Link from "next/link";
import home from "../assets/images/homepage.png";
import arrowright from "../assets/images/arrowright.png";
import Image from "next/image";

const FavoritesPage = ({ initialProducts = [], isAuthenticated = false }) => {
  const router = useRouter();
  const { favorites, setFavorites, loading: favoritesLoading } = useFavorites();
  const [layout, setLayout] = useState("grid"); // grid or list
  const [products, setProducts] = useState(initialProducts);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch favorite products whenever favorites change
  useEffect(() => {
    const fetchFavoriteProducts = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        const response = await getUserFavoriteProducts();
        if (response.success) {
          setProducts(response.data.products);
        }
      } catch (error) {
        console.error("Error fetching favorite products:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFavoriteProducts();
  }, [favorites, isAuthenticated]);

  // Function to handle product navigation
  const handleProductClick = (product) => {
    // This is a simplified version - you'll need to determine the category
    // For now, we'll just use a placeholder category
    router.push(
      `/electronics/${encodeURIComponent(
        product.name.toLowerCase().replace(/ /g, "-")
      )}`
    );
  };

  // If user is not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen pt-32 pb-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center justify-center text-center py-16">
            <h1 className="text-3xl font-bold mb-6 transformaSansBold">
              My Favorites
            </h1>
            <p className="text-lg mb-8 max-w-md">
              Please log in to view and manage your favorite products.
            </p>
            <button
              onClick={() => router.push("/")}
              className="bg-[#E0482F] text-white px-8 py-3 rounded-full hover:bg-[#c13e29] transition-colors"
            >
              Log In
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If there are no favorite products, show empty state
  if (products.length === 0 && !favoritesLoading && !isLoading) {
    return (
      <div className="min-h-screen pt-32 pb-16 bg-white">
        <div className="container mx-auto px-4 mt-40">
          <div className="flex flex-col items-center justify-center text-center py-16">
            <h1 className="text-3xl font-bold mb-6 transformaSansBold text-black">
              My Favorites
            </h1>
            <div className="mb-8 flex justify-center">
              <FaHeart className="text-pink-500 w-32 h-32" />
            </div>
            <p className="text-lg mb-8 max-w-md text-black Sora">
              You haven't added any products to your favorites yet.
            </p>
            <Link href="/">
              <button className="bg-[#E0482F] text-white px-8 py-3 rounded-full hover:bg-[#c13e29] transition-colors">
                Explore Products
              </button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-32 pb-16 bg-white">
      <div>
        {/* Header with back button */}
        <div className="bg-[#E0F069] py-3 mt-8">
          <div className="mx-auto container flex gap-6 flex-row">
            <Image src={home} className="w-6 h-6 my-auto" />
            <Image src={arrowright} className="w-3 my-auto h-4" />
            <div className="border border-gray-400 Sora text-black p-2 rounded-2xl">
              favourites{" "}
            </div>
            <Image src={arrowright} className="w-3 my-auto h-4" />
            <div className="border border-gray-400 Sora text-black p-2 rounded-2xl">
              {products.length} {products.length === 1 ? "product" : "products"}
            </div>
          </div>
        </div>

        {/* Products grid/list */}
        <div
          className={`grid mt-8 ${
            layout === "grid"
              ? "grid-cols-1 container mx-auto  md:grid-cols-2 lg:grid-cols-4"
              : "grid-cols-1"
          } gap-6`}
        >
          {favoritesLoading || isLoading ? (
            // Loading skeleton
            // Array.from({ length: 4 }).map((_, index) => (
            //   <div
            //     key={`skeleton-${index}`}
            //     className="bg-white p-4 border border-[#EBEBEB] rounded-lg relative animate-pulse"
            //   >
            //     <div className="absolute top-2 left-2 bg-gray-200 w-16 h-5 rounded-lg"></div>
            //     <div className="absolute top-2 right-2 bg-gray-200 w-8 h-8 rounded-full"></div>
            //     <div className="w-full h-40 bg-gray-200 rounded-lg mt-2 mb-4"></div>
            //     <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            //     <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
            //     <div className="h-8 bg-gray-200 rounded-full w-1/3 mb-4"></div>
            //     <div className="flex gap-3">
            //       <div className="h-6 bg-gray-200 rounded w-16"></div>
            //       <div className="h-6 bg-gray-200 rounded w-16"></div>
            //     </div>
            //   </div>
            // ))
            <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
              <div className="text-center">
                <FaSpinner className="animate-spin text-4xl text-white mx-auto mb-4" />
                <p className="text-white font-medium">Loading products...</p>
              </div>
            </div>
          ) : (
            // Actual products
            products.map((product) => (
              <div
                key={product.id}
                data-product-id={product.id}
                className={`bg-white p-4 border cursor-pointer border-[#EBEBEB] rounded-lg relative hover:shadow-lg hover:scale-[1.02] transition-all duration-200 group ${
                  layout === "list" ? "flex gap-6" : ""
                }`}
                onClick={() => handleProductClick(product)}
              >
                <span className="absolute top-2 left-2 bg-[#F0381A] text-white px-2 py-1 text-xs rounded-lg Sora">
                  Favourite
                </span>
                {favoritesLoading ? (
                  <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full">
                    <div className="bg-gray-100 p-1.5 rounded-full">
                      <div className="animate-pulse w-5 h-5 rounded-full bg-gray-300"></div>
                    </div>
                  </div>
                ) : (
                  <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full cursor-pointer hover:bg-opacity-100 transition">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();

                        // Get the product element for animation
                        const productElement =
                          e.currentTarget.closest("[data-product-id]");

                        // Animate the removal
                        if (productElement) {
                          productElement.style.opacity = "0";
                          productElement.style.transform = "scale(0.95)";
                          productElement.style.transition = "all 0.3s ease";
                        }

                        // Update the favorites set
                        toggleProductFavorite(product.id, setFavorites);

                        // Remove from local state after animation
                        setTimeout(() => {
                          setProducts((currentProducts) =>
                            currentProducts.filter((p) => p.id !== product.id)
                          );
                        }, 300);
                      }}
                      className="bg-gray-100 p-1.5 rounded-full hover:bg-gray-200 transition-colors"
                    >
                      <FaHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                    </button>
                  </div>
                )}
                <img
                  src={
                    product.featuredImage ||
                    product.productImage?.[0] ||
                    "/placeholder.png"
                  }
                  alt={product.name}
                  className="w-full h-40 mt-2 object-contain rounded-md"
                  width={500}
                  height={500}
                />
                <div className="h-18">
                  <h3 className="text-sm text-left Sora text-gray-400 mt-2">
                    {product.category || "Electronics"}
                  </h3>
                  <h3 className="text-lg font-semibold Sora text-left text-gray-900 line-clamp-2 group-hover:text-[#F0381A] transition-colors">
                    {product.name}
                  </h3>
                </div>
                <div className="flex items-center flex-row mb-2 mt-5 ">
                  <div className="bg-[#F0F0F0] flex flex-row px-2 py-1 rounded-full">
                    <div className="flex mr-2 my-auto">{renderStars(4)}</div>
                    <span className="text-sm text-gray-600">{4}</span>
                  </div>
                </div>
                <div className="flex flex-row gap-3 mt-2 my-auto">
                  {product.priceOld && (
                    <p className="text-gray-500 my-auto text-base line-through">
                      {`Rs ${product.priceOld?.toLocaleString()}`}
                    </p>
                  )}
                  <p className="text-red-500 font-bold text-lg">
                    {`Rs ${product.price?.toLocaleString()}`}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default FavoritesPage;
