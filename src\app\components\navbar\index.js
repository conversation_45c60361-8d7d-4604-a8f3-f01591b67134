"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, FaUserCircle } from "react-icons/fa";
import { FaBell } from "react-icons/fa6";
import { RxDropdownMenu } from "react-icons/rx";
import SearchBar from "../SearchBar/index";
import Register from "../../pages/Auth/register";
import Login from "../../pages/Auth/login";
import { usePathname, useRouter } from "next/navigation";
import logo from "@/app/assets/images/logopricio.png";
import Image from "next/image";
import menu from "@/app/assets/images/menu.png";
import Link from "next/link";
import { useDispatch, useSelector } from "react-redux";
import {
  setScrollToFalse,
  setScrollToTrue,
} from "@/redux/reducers/Scroll/scrollSlice";
import CategoriesModal from "../../components/categoriesModal/index";
import GlobalDropdown from "../Dropdown/GlobalDropdown";
import DropdownItem from "../Dropdown/DropdownItem";
import NotificationDropdown from "../Dropdown/NotificationDropdown";
import { logoutUser } from "@/server/auth/logout";
import { toast, ToastContainer, Zoom } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const NavbarClient = ({ categories, token }) => {
  const dispatch = useDispatch();
  const pathname = usePathname();
  const isHomePage = pathname === "/";
  const isScrolled = useSelector((state) => state.scroll.isScrolled);
  const [userDropdown, setUserdropdown] = useState(false);
  const [categorydropdown, setCategoryDropdown] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const router = useRouter();
  const [activeLink, setActiveLink] = useState("");
  const [localScroll, setLocalScroll] = useState(isScrolled);
  const [SelectedTab, setSelectedTab] = useState("Deals");
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationMenuOpen, setNotificationMenuOpen] = useState(false);

  const handleLogout = async () => {
    setUserdropdown(false);
    setUserMenuOpen(false); // Close the dropdown immediately
    try {
      await logoutUser();
      localStorage.removeItem("user");
      toast.success("Logged out successfully", {
        style: { width: "450px" },
      });
      window.location.href = "/";
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Failed to logout. Please try again.", {
        style: { width: "450px" },
      });
    }
  };

  const handleScroll = () => {
    const currentScroll = window.scrollY > 50;
    setLocalScroll(currentScroll);
    if (currentScroll) {
      dispatch(setScrollToTrue(true));
    } else {
      dispatch(setScrollToFalse(false));
    }
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  });

  useEffect(() => {
    setLocalScroll(isScrolled);
  }, [isScrolled]);

  return (
    <>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <div
        className={`fixed top-0 left-0 right-0 z-20 w-full bg-white transition-all duration-300 ${
          localScroll || !isHomePage ? "pt-2" : "pt-4"
        } ${userDropdown ? "bg-opacity-75" : "bg-opacity-100"}`}
      >
        {/* 🔹 Navbar left Section */}
        <div className="flex items-center justify-between container mx-auto py-3 text-2xl">
          <div className="flex flex-row gap-3">
            <div
              className="transformaScriptSemibold text-[#E0482F] my-auto relative"
              onClick={() => {
                setCategoryDropdown(!categorydropdown);
              }}
            >
              <Image
                alt="logo"
                src={menu}
                height={30}
                width={30}
                className="cursor-pointer"
              />
              {categorydropdown && (
                <div className="fixed inset-0 bg-black opacity-60 z-30"></div>
              )}
            </div>
            <div
              className="transformaScriptSemibold text-[#E0482F] cursor-pointer"
              onClick={() => router.push(`/`)}
            >
              <Image alt="logo" src={logo} height={80} width={80} />
            </div>
          </div>

          {/* 🔹 Search Bar Section */}
          <div
            className={`absolute left-1/2 transform -translate-x-1/2 transition-all duration-500 ${
              localScroll || !isHomePage
                ? "my-auto scale-75 w-[40%]"
                : "top-15 scale-100 w-[744px] mt-6 mb-8"
            }`}
          >
            <SearchBar
              customWidth="w-full"
              customBorder="border border-[#D9D9D999] shadow rounded-full focus:border-black focus:shadow-sm"
              customPadding="py-4 px-8"
            />
          </div>

          {/* 🔹 Mid Section */}
          {!localScroll && isHomePage && (
            <div className="flex flex-row items-center Sora mx-auto pl-40 gap-2 text-black">
              <Link href="#">
                <div
                  className={`flex flex-row gap-1 py-2 px-4 cursor-pointer rounded transformaSansSemibold text-sm ${
                    activeLink === "deals" ? "underline text-orange-500" : ""
                  }`}
                  onClick={() => setActiveLink("deals")}
                >
                  Deals
                </div>
              </Link>
              <Link href="#">
                <div
                  className={`flex flex-row gap-1 py-2 px-4 cursor-pointer rounded transformaSansSemibold text-sm ${
                    activeLink === "best-picks"
                      ? "underline text-orange-500"
                      : ""
                  }`}
                  onClick={() => setActiveLink("best-picks")}
                >
                  Best picks
                </div>
              </Link>
            </div>
          )}

          {/* 🔹 Navbar right Section */}
          <div className="flex flex-row items-center Sora text-black gap-1">
            <GlobalDropdown
              id="notification-dropdown"
              isOpen={notificationMenuOpen}
              setIsOpen={setNotificationMenuOpen}
              position="right-0 top-full mt-2"
              width="w-auto"
              maxHeight="h-auto"
              zIndex="z-50"
              trigger={
                <div className="flex flex-row gap-1 py-2 px-4 cursor-pointer rounded hover:bg-gray-200 transformaSansSemibold text-sm relative">
                  <FaBell className="my-auto w-5 h-5" />{" "}
                  {!localScroll && isHomePage && "Price Alerts"}
                  <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    3
                  </span>
                </div>
              }
            >
              <NotificationDropdown
                isOpen={notificationMenuOpen}
                setIsOpen={setNotificationMenuOpen}
              />
            </GlobalDropdown>
            {token && (
              <div
                className="flex flex-row gap-1 py-2 px-4 cursor-pointer rounded hover:bg-gray-200 transformaSansSemibold text-sm"
                onClick={() => router.push("/favorites")}
              >
                <FaRegHeart className="my-auto w-4 h-4" />{" "}
                {!localScroll && isHomePage && "My Favourites"}
              </div>
            )}
            {/* 🔹 User Dropdown */}
            <div className="relative">
              {!token ? (
                <div
                  className="border cursor-pointer hover:shadow-xl z-80 border-[#D9D9D999] py-2 px-4 flex flex-row rounded-full gap-3 bg-white"
                  onClick={() => {
                    setUserdropdown(!userDropdown);
                    if (!userDropdown) {
                      setShowRegister(false);
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    <RxDropdownMenu className="text-black" />
                    <FaUserCircle className="text-black" />
                  </div>
                </div>
              ) : (
                <GlobalDropdown
                  id="user-dropdown"
                  isOpen={userMenuOpen}
                  setIsOpen={setUserMenuOpen}
                  trigger={
                    <div className="border cursor-pointer hover:shadow-xl z-80 border-[#D9D9D999] py-2 px-4 flex flex-row rounded-full gap-3 bg-white">
                      <div className="flex items-center gap-3">
                        <RxDropdownMenu className="text-black" />
                        <FaUserCircle className="text-black" />
                      </div>
                    </div>
                  }
                  width="w-48"
                  maxHeight="h-auto"
                >
                  <ul>
                    <DropdownItem onClick={() => router.push("/profile")}>
                      Profile
                    </DropdownItem>
                    <DropdownItem onClick={() => router.push("/settings")}>
                      Settings
                    </DropdownItem>
                    <DropdownItem onClick={() => router.push("/orders")}>
                      My Orders
                    </DropdownItem>
                    <DropdownItem onClick={handleLogout}>Logout</DropdownItem>
                  </ul>
                </GlobalDropdown>
              )}
              {userDropdown && !token && (
                <div className="fixed inset-0 bg-black opacity-60 z-30"></div>
              )}
              {userDropdown && !token && !showRegister && (
                <Login
                  setUserdropdown={setUserdropdown}
                  setShowRegister={setShowRegister}
                />
              )}
              {userDropdown && !token && showRegister && (
                <Register
                  setUserdropdown={setUserdropdown}
                  setShowRegister={setShowRegister}
                />
              )}
            </div>
          </div>
        </div>
        {!localScroll && isHomePage && (
          <hr
            className={`border-[0.5px] ${
              !localScroll && "mt-18"
            }  border-gray-100 opacity-80`}
          />
        )}
        {categorydropdown && (
          <CategoriesModal
            setCategoryDropdown={setCategoryDropdown}
            categoriesParent={categories}
            SelectedTab={SelectedTab}
            setSelectedTab={setSelectedTab}
          />
        )}
      </div>
    </>
  );
};

export default NavbarClient;
