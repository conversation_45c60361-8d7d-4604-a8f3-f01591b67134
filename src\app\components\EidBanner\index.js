import React from "react";
import Image from "next/image";
import Mobile from "@/app/assets/images/mobiles.png";

const EidBanner = ({
  background,
  headingColor = null,
  subheadingColor = null,
  buttonColor = null,
  buttonBorderColor = null,
  image = null,
  heading = null,
  onClick = null,
}) => {
  return (
    <section
      className={`${
        background ? background : "bg-white"
      } py-4 h-90 pl-10 rounded-xl flex flex-col md:flex-row items-center justify-between`}
    >
      {/* Left Side - Text */}
      <div className="text-left flex flex-col h-full relative w-full md:w-1/2">
        <div className="flex flex-col mt-5 h-full">
          <h2
            className={`text-7xl font-extrabold  ${
              headingColor ? headingColor : "text-black"
            } transformaSansBold`}
          >
            {heading || (
              <>
                Best <br /> Eid Picks
              </>
            )}
          </h2>
          <p
            className={`${
              subheadingColor ? subheadingColor : "text-gray-600"
            } text-lg mt-2`}
          >
            Best Eid exclusive deals
          </p>
        </div>

        <button
          className={`absolute bottom-0 mt-5 px-3 py-2 ${
            buttonColor ? buttonColor : "text-black"
          } border-1 ${
            buttonBorderColor ? buttonBorderColor : "border-black"
          } rounded-full text-md transition cursor-pointer hover:bg-black hover:text-white`}
          onClick={onClick}
        >
          Discover now
        </button>
      </div>

      {/* Right Side - Image */}
      <div className="relative mt-6 md:mt-0 w-full md:w-1/2">
        <Image
          src={image ? image : Mobile}
          alt="Eid Picks"
          className="relative w-72 md:w-96"
        />
      </div>
    </section>
  );
};

export default EidBanner;
