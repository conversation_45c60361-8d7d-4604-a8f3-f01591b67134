"use client";
import React from "react";
import PopularProducts from "@/app/components/popular";
import EidBanner from "@/app/components/EidBanner";
import Fire from "@/app/assets/images/fire.png";
// import { useRouter } from "next/navigation";
import Image from "next/image";

const BestWeek1 = () => {
  // const router = useRouter();

  const handleNavigation = () => {
    // router.push(`/categories`);
  };

  return (
    <div className="py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-row mb-6 gap-5">
          <h2 className="text-2xl font-bold transformaSansSemibold text-black  text-left">
            Best of this week
          </h2>
          <Image
            src={Fire}
            alt="fire"
            className="object-contain"
            height={20}
            width={20}
          />
        </div>
        <div className="grid grid-cols-12 gap-10">
          <div className="col-span-7">
            <PopularProducts slidePercentage={33.33} />
          </div>
          <div className="col-span-5">
            <EidBanner
              background="bg-[#F0381A]"
              headingColor="text-white"
              subheadingColor="text-white"
              buttonColor="text-white"
              buttonBorderColor="text-white"
              onClick={() => handleNavigation()}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BestWeek1;
