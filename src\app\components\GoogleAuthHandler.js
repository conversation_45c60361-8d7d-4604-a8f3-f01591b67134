"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "react-toastify";

/**
 * Component to handle Google authentication success/failure
 * This component checks URL parameters for Google login results
 * and updates the UI accordingly
 */
export default function GoogleAuthHandler() {
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check if we have a Google login success parameter
    const googleLoginSuccess = searchParams.get("googleLoginSuccess");
    const error = searchParams.get("error");
    const userDataParam = searchParams.get("userData");

    console.log("GoogleAuthHandler: Checking URL parameters");
    console.log("- googleLoginSuccess:", googleLoginSuccess);
    console.log("- error:", error);
    console.log("- userDataParam exists:", !!userDataParam);

    if (googleLoginSuccess === "true") {
      console.log("GoogleAuthHandler: Login successful, showing toast");
      // Show success toast
      toast.success("Google login successful", {
        style: { width: "450px" },
      });

      // If we have user data, store it in localStorage
      if (userDataParam) {
        try {
          const userData = JSON.parse(userDataParam);
          console.log("GoogleAuthHandler: Parsed user data:", userData);
          localStorage.setItem("user", JSON.stringify(userData));
          console.log("GoogleAuthHandler: User data stored in localStorage");

          // Reload the page to update UI state
          // We use a small timeout to ensure the toast is visible and loading overlay is shown
          console.log("GoogleAuthHandler: Setting timeout for page reload");
          setTimeout(() => {
            // Remove the query parameters before reloading
            const url = new URL(window.location.href);
            url.searchParams.delete("googleLoginSuccess");
            url.searchParams.delete("userData");
            window.history.replaceState({}, "", url);
            console.log("GoogleAuthHandler: URL parameters removed");

            // Force a hard reload to ensure the server component (Navbar) re-renders with the token
            console.log("GoogleAuthHandler: Reloading page");
            window.location.reload();
          }, 2000); // Increased timeout to ensure loading overlay is visible
        } catch (error) {
          console.error("GoogleAuthHandler: Error parsing user data:", error);
          toast.error("Error processing login data", {
            style: { width: "450px" },
          });
        }
      } else {
        console.warn("GoogleAuthHandler: No user data received");
        toast.warning("Login successful but no user data received", {
          style: { width: "450px" },
        });
      }
    } else if (error) {
      console.error("GoogleAuthHandler: Login failed with error:", error);
      // Show error toast
      toast.error(`Google login failed: ${error}`, {
        style: { width: "450px" },
      });

      // Remove the error parameter from the URL
      const url = new URL(window.location.href);
      url.searchParams.delete("error");
      window.history.replaceState({}, "", url);
    }
  }, [searchParams]);

  // This component doesn't render anything
  return null;
}
