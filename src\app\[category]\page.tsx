"use server";

import { getProductsByCategory } from "../../server/products/index";
import { getSubcategoriesByParentSlug } from "@/server/categories";
import Category from "./category";

type Params = Promise<{ category: string }>;

export default async function CategoryFetch({ params }: { params: Params }) {
  const { category } = await params;

  const categoriesProducts = await getProductsByCategory(category);
  const subcategories = await getSubcategoriesByParentSlug(category);

  return (
    <Category
      categoriesProducts={categoriesProducts}
      subcategories={subcategories}
      category={category}
    />
  );
}
