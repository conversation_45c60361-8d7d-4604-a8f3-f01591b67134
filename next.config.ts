/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      "public.readdy.ai",
      "plus.unsplash.com",
      "mofets.com",
      "res.cloudinary.com",
      "hawkeye.pricio.app",
      "randomuser.me",
    ], // Add all domains you're loading images from
  },
  webpack: (config, { isServer }) => {
    // Handle ONNX runtime for transformers
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    // Ignore ONNX runtime node bindings in client-side builds
    config.externals = config.externals || [];
    config.externals.push({
      "onnxruntime-node": "commonjs onnxruntime-node",
    });

    // Handle .node files
    config.module.rules.push({
      test: /\.node$/,
      use: "ignore-loader",
    });

    return config;
  },
  // External packages for server components
  serverExternalPackages: ["@xenova/transformers"],
};

module.exports = nextConfig;
