"use client";
import React, { useState, useEffect } from "react";
import Sidebar from "../components/sidebar";
import { <PERSON>aTh, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>aSpinner } from "react-icons/fa";
import Image from "next/image";
import cta from "../assets/images/cta.png";
import CategoryProducts from "./categoryProducts";
import home from "../assets/images/homepage.png";
import arrowright from "../assets/images/arrowright.png";
import { useFavorites } from "../hooks/useFavorites";

export default function Category({
  categoriesProducts,
  subcategories,
  category,
}) {
  const [selectedSort, setSelectedSort] = useState("popularity");
  const [layout, setLayout] = useState("grid");
  const [pageLoading, setPageLoading] = useState(true);
  const { loading: favoritesLoading } = useFavorites();
  const rawCategory = category || "";

  useEffect(() => {
    // Set loading to false when products and favorites are loaded
    if (categoriesProducts && !favoritesLoading) {
      // Add a small delay to ensure smooth transition
      const timer = setTimeout(() => {
        setPageLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [categoriesProducts, favoritesLoading]);
  // Function to capitalize each word in a string
  const capitalizeWords = (str) => {
    return str
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };
  const categoryName = capitalizeWords(rawCategory.replace(/-/g, " "));
  const handleSortChange = (event) => {
    setSelectedSort(event.target.value);
    if (onSortChange) onSortChange(event.target.value);
  };

  const handleLayoutChange = (newLayout) => {
    setLayout(newLayout);
    if (onLayoutChange) onLayoutChange(newLayout);
  };

  return (
    // mt-[14rem]
    <div className="min-h-screen py-12 mt-22">
      {pageLoading && (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
          <div className="text-center">
            <FaSpinner className="animate-spin text-4xl text-white mx-auto mb-4" />
            <p className="text-white font-medium">Loading products...</p>
          </div>
        </div>
      )}
      <div className="bg-[#E0F069] py-3 mt-8">
        <div className="mx-auto container flex gap-6 flex-row">
          <Image src={home} className="w-6 h-6 my-auto" />
          <Image src={arrowright} className="w-3 my-auto h-4" />
          <div className="border border-gray-400 Sora text-black p-2 rounded-2xl">
            {categoryName}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-10 container mx-auto px-4 mt-8">
        <div className="col-span-3">
          <Sidebar subcategories={subcategories} />
        </div>
        <div className="col-span-9">
          <div className="flex justify-between mb-8">
            <h2 className="text-2xl font-bold text-black transformaSansBold text-left">
              {categoryName}
            </h2>
            <div className="flex items-center gap-4">
              <label className="text-gray-500 text-sm transformaSansNormal">
                Sort by:
              </label>
              <select
                value={selectedSort}
                onChange={handleSortChange}
                className="border border-gray-300 bg-[#F8F7F1] transformaSansNormal text-black rounded-md  py-1 text-sm"
              >
                <option value="popularity">Popularity</option>
                <option value="price-low-high">Price: Low to High</option>
                <option value="price-high-low">Price: High to Low</option>
                <option value="newest">Newest</option>
              </select>

              {/* Layout Toggle Buttons */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleLayoutChange("grid")}
                  className={`p-2 rounded ${
                    layout === "grid"
                      ? "bg-red-500 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                >
                  <FaTh size={18} />
                </button>

                <button
                  onClick={() => handleLayoutChange("list")}
                  className={`p-2 rounded ${
                    layout === "list"
                      ? "bg-red-500 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                >
                  <FaThList size={18} />
                </button>
              </div>
            </div>
          </div>
          <CategoryProducts
            fullView={true}
            layout={layout}
            categoriesProducts={categoriesProducts}
          />
          <div className="mx-auto flex justify-center">
            <button className="mt-8 px-3 py-2 text-black border-1 border-black rounded-full text-sm transition hover:bg-black hover:text-white">
              Load more
            </button>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 mt-10">
        <Image src={cta} alt="cta" />
      </div>
    </div>
  );
}
