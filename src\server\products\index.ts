"use server";

import { db } from "@/lib/db";
import {
  categories,
  products,
  product_categories_category,
} from "@/lib/schema";
import { eq } from "drizzle-orm";

export async function getProductsByCategory(slug: string) {
  try {
    const result = await db
      .select({
        id: products.id,
        name: products.name,
        price: products.price,
        featuredImage: products.featuredImage,
        productImage: products.productImage,
        priceOld: products.priceOld,
      })
      .from(products)
      .innerJoin(
        product_categories_category,
        eq(products.id, product_categories_category.productId)
      )
      .innerJoin(
        categories,
        eq(product_categories_category.categoryId, categories.id)
      )
      .where(eq(categories.slug, slug))
      .limit(100);
    return result;
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw new Error("Could not fetch categories");
  }
}
