import React, { useState } from "react";
import closeIcon from "../../assets/images/closeIcon.png";
import Image from "next/image";

// Custom checkbox component with red background when checked
const CustomCheckbox = ({ className = "", ...props }) => {
  return (
    <input
      type="checkbox"
      className={`w-5 h-5 rounded-sm border border-[#00000070] checked:bg-red-500 checked:border-red-500 appearance-none relative ${className}`}
      style={{
        backgroundImage:
          "url('data:image/svg+xml;charset=utf-8,%3Csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3E%3Cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3E%3C/svg%3E')",
        backgroundSize: "100% 100%",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
      {...props}
    />
  );
};

// Custom radio button component with red background when checked
const CustomRadio = ({ className = "", ...props }) => {
  return (
    <input
      type="radio"
      className={`w-5 h-5 rounded-full border border-[#00000070] checked:bg-red-500 checked:border-red-500 appearance-none relative ${className}`}
      style={{
        backgroundImage:
          "url('data:image/svg+xml;charset=utf-8,%3Csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3E%3Ccircle cx=%278%27 cy=%278%27 r=%274%27/%3E%3C/svg%3E')",
        backgroundSize: "75% 75%",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
      {...props}
    />
  );
};

const FiltersModal = ({ setFiltersModal }) => {
  // State for tracking selected options
  const [sortBy, setSortBy] = useState("lowest-price");
  const [deliveryCharges, setDeliveryCharges] = useState("show-all");

  return (
    <main className="fixed inset-0 flex items-center justify-center z-50">
      <div
        className="bg-black opacity-80 absolute inset-0"
        onClick={() => setFiltersModal(false)}
      ></div>
      <div className="bg-white rounded-3xl w-full max-w-md h-screen max-h-[95vh] my-auto mx-auto relative overflow-y-auto">
        {/* Header with close button */}
        <div className="sticky top-0 bg-white p-5 border-b border-gray-200 z-10">
          <div className="relative flex items-center justify-center">
            <h2 className="font-bold text-xl text-black Sora">Filter</h2>
            <button
              className="absolute right-0 top-0"
              onClick={() => setFiltersModal(false)}
            >
              <Image
                src={closeIcon}
                height={24}
                width={24}
                alt="close-icon"
                className="cursor-pointer"
              />
            </button>
          </div>
        </div>

        {/* Filter content */}
        <div className="p-5 overflow-y-auto">
          {/* Display Screen Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-3 transformaSansSemibold text-black">
              Display Screen
            </h3>
            <div className="space-y-3 Sora">
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700">Only in stock</span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700">Price incl. delivery</span>
              </label>
              <div>
                <label className="flex items-center gap-3 cursor-pointer">
                  <CustomCheckbox />
                  <span className="text-gray-700">Used products</span>
                </label>
                <p className="text-sm text-gray-500 ml-8">Price from ₹6,500</p>
              </div>
            </div>
          </div>

          {/* Store Options Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-3 transformaSansSemibold text-black">
              Store Options
            </h3>
            <div className="space-y-3 Sora">
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700">Show unverified stores</span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700 flex items-center">
                  Samsung Authorized
                  <svg
                    className="w-4 h-4 ml-2 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                </span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700 flex items-center">
                  Resellers with Buyer Protection
                  <svg
                    className="w-4 h-4 ml-2 text-gray-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    ></path>
                  </svg>
                </span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700 flex items-center">
                  Resellers with Klarna
                  <span className="ml-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-black text-base">
                    K
                  </span>
                </span>
              </label>
              <div>
                <label className="flex items-center gap-3 cursor-pointer">
                  <CustomCheckbox />
                  <span className="text-gray-700">International stores</span>
                </label>
                <p className="text-sm text-gray-500 ml-8">Price from ₹6,500</p>
              </div>
            </div>
          </div>

          {/* Sort by Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-3 text-black transformaSansSemibold">
              Sort by
            </h3>
            <div className="space-y-3 Sora">
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomRadio
                  name="sort"
                  value="fastest-delivery"
                  checked={sortBy === "fastest-delivery"}
                  onChange={() => setSortBy("fastest-delivery")}
                />
                <span className="text-gray-700">Fastest Delivery</span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomRadio
                  name="sort"
                  value="lowest-price"
                  checked={sortBy === "lowest-price"}
                  onChange={() => setSortBy("lowest-price")}
                />
                <span className="text-gray-700">Lowest Price</span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomRadio
                  name="sort"
                  value="recommended"
                  checked={sortBy === "recommended"}
                  onChange={() => setSortBy("recommended")}
                />
                <span className="text-gray-700">Recommended</span>
              </label>
            </div>
          </div>

          {/* Delivery Charges Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-3 text-black transformaSansSemibold">
              Delivery Charges
            </h3>
            <div className="space-y-3 Sora">
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomRadio
                  name="delivery"
                  value="show-all"
                  checked={deliveryCharges === "show-all"}
                  onChange={() => setDeliveryCharges("show-all")}
                />
                <span className="text-gray-700">Show all</span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700">Home delivery</span>
              </label>
              <label className="flex items-center gap-3 cursor-pointer">
                <CustomCheckbox />
                <span className="text-gray-700">Service Point</span>
              </label>
            </div>
          </div>
        </div>

        {/* Show results button */}
        <div className="sticky bottom-0 bg-white p-5 border-t border-gray-200">
          <button
            className="w-full py-3 bg-[#0B051D] text-white rounded-full font-medium"
            onClick={() => setFiltersModal(false)}
          >
            Show results
          </button>
        </div>
      </div>
    </main>
  );
};

export default FiltersModal;
