# Typesense Setup Guide with <PERSON><PERSON>

This guide explains how to set up Typesense using Docker for the search functionality in this application.

## Running Typesense with Docker

### Prerequisites

- Docker installed on your machine
- Docker Compose installed on your machine

### Step 1: Start Typesense Container

The project includes a `docker-compose.yml` file that's already configured for Typesense. To start Typesense:

```bash
docker-compose up -d
```

This will start the Typesense server in the background on port 8108.

### Step 2: Verify Typesense is Running

You can check if Typesense is running with:

```bash
docker ps
```

You should see a container named something like `frontend_typesense_1` in the list.

You can also check the logs:

```bash
docker-compose logs typesense
```

### Step 3: Configure Environment Variables

The default configuration in `.env.local` is already set up for the Docker Typesense installation:

```
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_ADMIN_API_KEY=xyz

NEXT_PUBLIC_TYPESENSE_HOST=localhost
NEXT_PUBLIC_TYPESENSE_PORT=8108
NEXT_PUBLIC_TYPESENSE_PROTOCOL=http
NEXT_PUBLIC_TYPESENSE_SEARCH_API_KEY=xyz
```

The API key (`xyz`) matches the one defined in the `docker-compose.yml` file.

## Syncing Data to Typesense

After setting up Typesense (either locally or on the cloud), you need to sync your product data:

```bash
yarn typesense:sync
```

This will import all your products from PostgreSQL into Typesense.

## Verifying the Setup

To verify that Typesense is working correctly:

1. Start your Next.js application:

   ```bash
   yarn dev
   ```

2. Go to the homepage and try searching for a product in the search bar
3. You should see search results appearing as you type

## Troubleshooting

### Connection Issues

If you're having trouble connecting to Typesense:

1. Make sure Typesense is running (if installed locally)
2. Check that your API keys are correct
3. Verify that the host, port, and protocol in your `.env.local` file are correct
4. Check the browser console for any errors

### Empty Search Results

If you're not seeing any search results:

1. Make sure you've synced your data using `yarn typesense:sync`
2. Check that your products were successfully imported
3. Try a broader search term

For more help, refer to the [Typesense documentation](https://typesense.org/docs/).
