# Vector Search Integration with Typesense

This guide explains how to use the newly integrated vector search capabilities in your Typesense-powered application.

## Overview

Vector search has been integrated into your existing Typesense setup, providing AI-powered semantic search capabilities alongside traditional text search. The system now supports three search modes:

1. **Text Search** - Traditional keyword-based search
2. **Vector Search** - AI-powered semantic search using embeddings
3. **Hybrid Search** - Combines both text and vector search for optimal results

## Features Added

### 1. Embedding Generation
- Uses `@xenova/transformers` with the `all-MiniLM-L6-v2` model
- Generates 384-dimensional embeddings for product content
- Combines product name, description, keywords, and category information

### 2. Updated Collection Schema
- Added `embedding` field of type `float[]` with 384 dimensions
- Maintains backward compatibility with existing text search

### 3. Vector Search Functions
- `vectorSearchProducts()` - Pure vector search
- `hybridSearchProducts()` - Combines text and vector search with weighted scoring
- Enhanced `searchProducts()` with `searchType` parameter

### 4. New Components
- `VectorSearchBar` - Search component with type selection
- `useVectorSearch` - React hook for vector search functionality
- Updated `SearchResults` to show search type indicators

## Setup Instructions

### 1. Install Dependencies

The required dependencies are already installed:
```bash
yarn add @xenova/transformers
```

### 2. Update Typesense Collection

You need to recreate your Typesense collection to include the vector field:

```bash
# Delete existing collection (this will remove all data)
yarn typesense:delete

# Sync with vector support (this will recreate the collection with embeddings)
yarn typesense:vector-sync
```

**Note**: The vector sync process will take longer than regular sync as it generates embeddings for each product.

### 3. Alternative: Use the Enhanced Sync

You can also use the enhanced server-side sync that includes embedding generation:

```bash
yarn typesense:sync
```

This will use the updated sync function that generates embeddings during the sync process.

## Usage

### Basic Vector Search

```javascript
import { searchProducts } from "@/server/typesense/search";

// Vector search
const results = await searchProducts("fast gaming laptop", {
  searchType: "vector",
  limit: 10
});

// Hybrid search (recommended)
const hybridResults = await searchProducts("budget smartphone", {
  searchType: "hybrid",
  limit: 10
});
```

### Using the Vector Search Component

```jsx
import VectorSearchBar from "@/app/components/VectorSearchBar";

function MyComponent() {
  return (
    <div>
      <VectorSearchBar />
    </div>
  );
}
```

### Using the Vector Search Hook

```jsx
import useVectorSearch from "@/hooks/useVectorSearch";

function MySearchComponent() {
  const {
    searchTerm,
    searchResults,
    searchType,
    handleSearchChange,
    handleSearchTypeChange,
    handleSearchSubmit
  } = useVectorSearch();

  return (
    <div>
      <input 
        value={searchTerm}
        onChange={handleSearchChange}
        placeholder="Search products..."
      />
      <select 
        value={searchType} 
        onChange={(e) => handleSearchTypeChange(e.target.value)}
      >
        <option value="text">Text Search</option>
        <option value="vector">Vector Search</option>
        <option value="hybrid">Hybrid Search</option>
      </select>
    </div>
  );
}
```

## Demo Page

Visit `/vector-search-demo` to see a live demonstration of the vector search capabilities, including:
- Interactive search with type selection
- Side-by-side comparison of search types
- Example queries that showcase semantic understanding

## Search Type Comparison

### Text Search
- **Best for**: Exact product names, model numbers, specific keywords
- **Example**: "iPhone 15" → Finds products with "iPhone" and "15" in the text
- **Speed**: Fastest

### Vector Search
- **Best for**: Natural language queries, conceptual searches, intent-based searches
- **Example**: "phone for photography" → Finds smartphones with good cameras
- **Speed**: Moderate (requires embedding generation)

### Hybrid Search (Recommended)
- **Best for**: General use, combines precision and intelligence
- **Example**: Balances exact matches with semantic understanding
- **Speed**: Moderate

## Configuration

### Embedding Model
The system uses `Xenova/all-MiniLM-L6-v2` which provides:
- 384-dimensional embeddings
- Good balance of speed and quality
- Runs locally without external API calls

### Search Weights (Hybrid Mode)
Default weights in hybrid search:
- Text search: 70%
- Vector search: 30%

You can adjust these in the `hybridSearchProducts` function.

## Performance Considerations

### Embedding Generation
- Initial sync with embeddings takes longer
- Embeddings are generated in batches to manage memory
- Consider running sync during off-peak hours

### Search Performance
- Vector search is slightly slower than text search
- Hybrid search provides the best balance
- Results are cached for improved performance

### Storage
- Each product requires ~1.5KB additional storage for embeddings
- 10,000 products ≈ 15MB additional storage

## Troubleshooting

### Common Issues

1. **"Collection not found" error**
   ```bash
   yarn typesense:vector-sync
   ```

2. **Embedding generation fails**
   - Check available memory (embeddings require ~2GB RAM)
   - Reduce batch size in sync configuration

3. **Slow search performance**
   - Ensure Typesense has adequate resources
   - Consider using hybrid search instead of pure vector search

4. **No vector search results**
   - Verify embeddings were generated during sync
   - Check that the collection schema includes the embedding field

### Logs and Debugging

Enable detailed logging by checking the console output during:
- Embedding generation
- Search operations
- Sync processes

## Future Enhancements

Potential improvements you could implement:

1. **Better Embedding Models**
   - Upgrade to larger, more accurate models
   - Use domain-specific models for your product category

2. **Advanced Hybrid Scoring**
   - Implement learning-based weight adjustment
   - Add user behavior signals to scoring

3. **Real-time Embedding Updates**
   - Generate embeddings when products are added/updated
   - Implement incremental sync for new products

4. **Search Analytics**
   - Track search type usage
   - Monitor search quality metrics
   - A/B test different search approaches

## API Reference

### searchProducts(query, options)

**Parameters:**
- `query` (string): Search query
- `options` (object):
  - `searchType`: "text" | "vector" | "hybrid"
  - `limit`: Number of results
  - `page`: Page number
  - `perPage`: Results per page
  - `filterBy`: Typesense filter string
  - `includeFields`: Fields to include in response

**Returns:** Promise resolving to search results with metadata

### vectorSearchProducts(query, options)

Direct vector search function with same parameters as above.

### hybridSearchProducts(query, options)

**Additional Parameters:**
- `textWeight`: Weight for text search (default: 0.7)
- `vectorWeight`: Weight for vector search (default: 0.3)

## Support

For issues or questions about the vector search integration:

1. Check the console logs for detailed error messages
2. Verify Typesense is running and accessible
3. Ensure the collection has been synced with vector support
4. Review the demo page for working examples
