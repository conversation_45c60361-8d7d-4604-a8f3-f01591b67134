const Typesense = require('typesense');
require('dotenv').config();

// Create Typesense client
const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: 'localhost',
      port: '8109',
      protocol: 'http',
    },
  ],
  apiKey: 'xyz',
  connectionTimeoutSeconds: 2,
});

async function checkCategories() {
  try {
    console.log('Connecting to Typesense...');
    const health = await typesenseClient.health.retrieve();
    console.log('Typesense server is reachable:', health);

    console.log('Checking if products collection exists...');
    const collection = await typesenseClient.collections('products').retrieve();
    console.log('Collection found:', collection.name);
    
    // Check if category fields are in the schema
    console.log('\nChecking schema for category fields:');
    const categoryFields = collection.fields.filter(field => 
      field.name === 'category_id' || 
      field.name === 'category_name' || 
      field.name === 'category_slug'
    );
    
    if (categoryFields.length === 0) {
      console.log('❌ No category fields found in schema');
    } else {
      console.log('✅ Found category fields in schema:');
      categoryFields.forEach(field => {
        console.log(`  - ${field.name} (${field.type})`);
      });
    }
    
    // Search for products and check if they have category information
    console.log('\nSearching for products to check category data...');
    const searchParameters = {
      q: '*',
      query_by: 'name',
      sort_by: '_text_match:desc',
      per_page: 5,
      include_fields: 'id,name,category_id,category_name,category_slug',
    };
    
    const searchResults = await typesenseClient
      .collections('products')
      .documents()
      .search(searchParameters);
    
    console.log(`Found ${searchResults.found} products in total`);
    
    if (searchResults.hits && searchResults.hits.length > 0) {
      console.log('\nChecking category data in search results:');
      let hasCategories = false;
      
      searchResults.hits.forEach((hit, index) => {
        console.log(`\nProduct ${index + 1}: ${hit.document.name} (ID: ${hit.document.id})`);
        
        if (hit.document.category_id) {
          console.log(`  ✅ category_id: ${hit.document.category_id}`);
          hasCategories = true;
        } else {
          console.log('  ❌ No category_id');
        }
        
        if (hit.document.category_name) {
          console.log(`  ✅ category_name: ${hit.document.category_name}`);
          hasCategories = true;
        } else {
          console.log('  ❌ No category_name');
        }
        
        if (hit.document.category_slug) {
          console.log(`  ✅ category_slug: ${hit.document.category_slug}`);
          hasCategories = true;
        } else {
          console.log('  ❌ No category_slug');
        }
      });
      
      if (hasCategories) {
        console.log('\n✅ SUCCESS: Products have category information!');
      } else {
        console.log('\n❌ FAILED: No category information found in products');
        console.log('You may need to run the sync script again');
      }
    } else {
      console.log('No products found in search results');
    }
    
  } catch (error) {
    console.error('Error checking categories:', error);
    if (error.httpStatus === 404) {
      console.error('Products collection not found. Please run the sync script first.');
    }
  }
}

// Run the check
checkCategories().catch(console.error);
