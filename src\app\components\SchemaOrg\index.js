"use client";

import React from "react";

/**
 * SchemaOrg component for adding structured data to pages
 * @param {Object} props - Component props
 * @param {string} props.type - Schema.org type (e.g., 'Product', 'Organization')
 * @param {Object} props.data - Data for the schema
 * @returns {JSX.Element} - Schema.org structured data as microdata
 */
const SchemaOrg = ({ type, data }) => {
  if (!type || !data) return null;

  switch (type) {
    case "Product":
      return (
        <div itemScope itemType="https://schema.org/Product">
          <meta itemProp="name" content={data.name} />
          {data.image &&
            (Array.isArray(data.image) ? (
              data.image.map((img, index) => (
                <meta key={index} itemProp="image" content={img} />
              ))
            ) : (
              <meta itemProp="image" content={data.image} />
            ))}
          {data.description && (
            <meta itemProp="description" content={data.description} />
          )}
          {data.brand && (
            <div itemProp="brand" itemScope itemType="https://schema.org/Brand">
              <meta itemProp="name" content={data.brand} />
            </div>
          )}
          {(data.price || data.price === 0) && (
            <div
              itemProp="offers"
              itemScope
              itemType="https://schema.org/Offer"
            >
              <meta itemProp="price" content={data.price} />
              <meta
                itemProp="priceCurrency"
                content={data.priceCurrency || "PKR"}
              />
              <meta
                itemProp="availability"
                content={data.availability || "https://schema.org/InStock"}
              />
              {data.url && <meta itemProp="url" content={data.url} />}
            </div>
          )}
          {data.aggregateRating && (
            <div
              itemProp="aggregateRating"
              itemScope
              itemType="https://schema.org/AggregateRating"
            >
              <meta
                itemProp="ratingValue"
                content={data.aggregateRating.ratingValue}
              />
              <meta
                itemProp="reviewCount"
                content={data.aggregateRating.reviewCount}
              />
            </div>
          )}
        </div>
      );

    case "Organization":
      return (
        <div itemScope itemType="https://schema.org/Organization">
          <meta itemProp="name" content={data.name} />
          {data.logo && <meta itemProp="logo" content={data.logo} />}
          {data.url && <meta itemProp="url" content={data.url} />}
          {data.sameAs &&
            data.sameAs.map((url, index) => (
              <meta key={index} itemProp="sameAs" content={url} />
            ))}
        </div>
      );

    case "BreadcrumbList":
      return (
        <div itemScope itemType="https://schema.org/BreadcrumbList">
          {data.itemListElement &&
            data.itemListElement.map((item, index) => (
              <div
                key={index}
                itemScope
                itemType="https://schema.org/ListItem"
                itemProp="itemListElement"
              >
                <meta itemProp="position" content={item.position} />
                <meta itemProp="name" content={item.name} />
                {item.item && <meta itemProp="item" content={item.item} />}
              </div>
            ))}
        </div>
      );

    default:
      return null;
  }
};

export default SchemaOrg;
