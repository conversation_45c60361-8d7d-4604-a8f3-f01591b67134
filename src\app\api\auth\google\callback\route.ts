import { NextRequest, NextResponse } from "next/server";
import { handleGoogleCallback } from "@/server/auth/google";

export async function GET(request: NextRequest) {
  try {
    console.log("Google callback received");

    // Get the authorization code from the URL
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get("code");
    const error = searchParams.get("error");

    console.log("Code:", code ? "Present" : "Missing");
    console.log("Error:", error);

    if (error) {
      console.error("Google OAuth error:", error);
      return NextResponse.redirect(
        new URL(`/?error=${encodeURIComponent(error)}`, request.url)
      );
    }

    if (!code) {
      console.error("Missing authorization code");
      return NextResponse.redirect(
        new URL("/?error=missing_code", request.url)
      );
    }

    console.log("About to exchange code for tokens");

    try {
      // Exchange the code for tokens and get user info
      const result = await handleGoogleCallback(code);
      console.log("Google callback result:", result);

      // Check if result has success property and it's false, or if it doesn't have success property at all
      if (result.success === false || !("success" in result)) {
        console.error("Google callback failed:", result.message);
        return NextResponse.redirect(
          new URL(`/?error=${encodeURIComponent(result.message)}`, request.url)
        );
      }

      console.log(
        "Google login successful, redirecting to home page with user data"
      );

      // Extract user data from the result to pass to the client
      const userData =
        "data" in result && result.data ? result.data.user : null;

      // Create a URL with success parameter and encode user data
      const redirectUrl = new URL("/?googleLoginSuccess=true", request.url);

      if (userData) {
        // Add user data as URL parameters for the client to use
        redirectUrl.searchParams.set("userData", JSON.stringify(userData));
      }

      // Redirect to home page with success parameter
      return NextResponse.redirect(redirectUrl);
    } catch (error) {
      console.error("Error in handleGoogleCallback:", error);
      return NextResponse.redirect(
        new URL("/?error=callback_processing_error", request.url)
      );
    }
  } catch (error) {
    console.error("Google callback error:", error);
    return NextResponse.redirect(new URL("/?error=server_error", request.url));
  }
}
