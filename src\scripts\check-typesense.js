// This script checks if Typesense is running and if the products collection exists
const Typesense = require("typesense");
require("dotenv").config();

// Create Typesense client
const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: process.env.TYPESENSE_HOST || "localhost",
      port: process.env.TYPESENSE_PORT || "8109",
      protocol: process.env.TYPESENSE_PROTOCOL || "http",
    },
  ],
  apiKey: process.env.TYPESENSE_ADMIN_API_KEY || "xyz",
  connectionTimeoutSeconds: 2,
});

async function checkTypesense() {
  console.log("Typesense Check Utility");
  console.log("======================");

  // 1. Check connection details
  console.log("\n1. Connection Details:");
  console.log("   Host:", process.env.TYPESENSE_HOST || "localhost");
  console.log("   Port:", process.env.TYPESENSE_PORT || "8109");
  console.log("   Protocol:", process.env.TYPESENSE_PROTOCOL || "http");
  console.log(
    "   API Key:",
    process.env.TYPESENSE_ADMIN_API_KEY
      ? "Set (not showing)"
      : "Not set (using default 'xyz')"
  );

  try {
    // 2. Check if Typesense server is reachable
    console.log("\n2. Checking if Typesense server is reachable...");
    const health = await typesenseClient.health.retrieve();
    console.log("   ✅ Typesense server is reachable!");
    console.log("   Health status:", health);

    // 3. List all collections
    console.log("\n3. Listing all collections...");
    const collections = await typesenseClient.collections().retrieve();
    console.log("   Found", collections.length, "collections:");
    collections.forEach((collection) => {
      console.log(
        `   - ${collection.name} (${collection.num_documents} documents)`
      );
    });

    // 4. Check if products collection exists
    console.log("\n4. Checking if 'products' collection exists...");
    try {
      const productsCollection = await typesenseClient
        .collections("products")
        .retrieve();
      console.log("   ✅ 'products' collection exists!");
      console.log("   Collection details:", productsCollection);
    } catch (error) {
      if (error.httpStatus === 404) {
        console.log("   ❌ 'products' collection does NOT exist!");
        console.log(
          "   You need to run 'yarn typesense:sync' to create it and import data."
        );
      } else {
        console.error(
          "   ❌ Error checking 'products' collection:",
          error.message
        );
      }
    }
  } catch (error) {
    console.error("\n❌ Error connecting to Typesense:", error.message);
    console.log("\nPossible issues:");
    console.log("1. Docker container is not running");
    console.log("2. Port is incorrect or blocked");
    console.log("3. API key is incorrect");
    console.log("\nTry running: docker-compose up -d");
  }
}

checkTypesense().catch(console.error);
