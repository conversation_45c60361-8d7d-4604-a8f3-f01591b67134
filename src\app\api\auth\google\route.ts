import { NextRequest, NextResponse } from "next/server";

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
// Use a hardcoded redirect URI for now to ensure it matches exactly what's in Google Console
const REDIRECT_URI = "http://localhost:3000/api/auth/google/callback";

export async function GET(request: NextRequest) {
  try {
    if (!GOOGLE_CLIENT_ID) {
      console.error("Google OAuth is not configured - missing client ID");
      return NextResponse.json(
        { error: "Google OAuth is not configured" },
        { status: 500 }
      );
    }

    const host = request.headers.get("host") || "localhost:3000";
    const protocol = host.includes("localhost") ? "http" : "https";
    const dynamicRedirectUri = `${protocol}://${host}/api/auth/google/callback`;

    console.log("Dynamic redirect URI:", dynamicRedirectUri);
    console.log("Static redirect URI:", REDIRECT_URI);

    // Use the dynamic URI which should match the actual request
    const params = new URLSearchParams({
      client_id: GOOGLE_CLIENT_ID,
      redirect_uri: dynamicRedirectUri,
      response_type: "code",
      scope: "openid email profile",
      access_type: "offline",
      prompt: "consent",
    });

    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
    console.log("Google Auth URL:", googleAuthUrl);

    // Redirect to Google's authentication page
    return NextResponse.redirect(googleAuthUrl);
  } catch (error) {
    console.error("Google auth error:", error);
    return NextResponse.json(
      { error: "Failed to initiate Google login" },
      { status: 500 }
    );
  }
}
