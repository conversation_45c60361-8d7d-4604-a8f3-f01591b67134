@tailwind base;
@tailwind components;
@tailwind utilities;
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* No scrollbar */
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px #4a9cb9;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #4a9cb9;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a9cb9;
}

* {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}

@font-face {
  font-family: "Transforma Bold";
  src: local("Transforma Bold"),
    url("../app/assets/fonts/TransformaSans/TransformaSans_Trial-Bold.otf")
      format("truetype");
  font-weight: bold;
}

@font-face {
  font-family: "Transforma Sans Semibold";
  src: local("Transforma Sans Semibold"),
    url("../app/assets/fonts/TransformaSans/TransformaSans_Trial-SemiBold.otf")
      format("truetype");
  font-weight: bold;
}

@font-face {
  font-family: "Transforma Script Semibold";
  src: local("Transforma Script Semibold"),
    url("../app/assets/fonts/TransformaSans/TransformaScript_Trial-SemiBold.otf")
      format("truetype");
  font-weight: bold;
}

@font-face {
  font-family: "Transforma Sans Regular";
  src: local("Transforma Sans Regular"),
    url("../app/assets/fonts/TransformaSans/TransformaSans_Trial-Regular.otf")
      format("truetype");
  font-weight: normal;
}

@font-face {
  font-family: "Transforma Sans Bold";
  src: local("Transforma Sans Bold"),
    url("../app/assets/fonts/TransformaSans/TransformaSans_Trial-Bold.otf")
      format("truetype");
  font-weight: bolder;
}

@font-face {
  font-family: "Sora Regular";
  src: local("Sora Regular"),
    url("../app/assets/fonts/Sora/Sora-Regular.ttf") format("truetype");
  font-weight: normal;
}

body {
  color: var(--foreground);
  background: white;
  font-family: Arial, Helvetica, sans-serif;
}

.transformaSansSemibold {
  font-family: "Transforma Sans Semibold" !important;
}

.transformaSansBold {
  font-family: "Transforma Sans Bold" !important;
}

.transformaScriptSemibold {
  font-family: "Transforma Script Semibold" !important;
}

.transformaSansNormal {
  font-family: "Transforma Sans Regular" !important;
}

.Sora {
  font-family: "Sora Regular";
}

.container_custom {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 300px) {
  .container_custom {
    max-width: 300px;
  }
}

@media (min-width: 500px) {
  .container_custom {
    max-width: 500px;
  }
}

@media (min-width: 768px) {
  .container_custom {
    max-width: 768px;
  }
}

@media (min-width: 900px) {
  .container_custom {
    max-width: 900px;
  }
}

@media (min-width: 1000px) {
  .container_custom {
    max-width: 1000px;
  }
}

@media (min-width: 1024px) {
  .container_custom {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container_custom {
    max-width: 1280px;
  }
}

@media (min-width: 1330px) {
  .container_custom {
    max-width: 1330px;
  }
}

@media (min-width: 1536px) {
  .container_custom {
    max-width: 1536px;
  }
}

@media (min-width: 1700px) {
  .container_custom {
    max-width: 1700px;
  }
}

@media (min-width: 2160px) {
  .container_custom {
    max-width: 2160px;
  }
}
