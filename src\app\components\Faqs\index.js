"use client";
import React, { useState, useRef, useEffect } from "react";
import { FiChevronDown } from "react-icons/fi";

export default function FAQSection({ productDetails }) {
  const [openIndex, setOpenIndex] = useState(null);
  const answerRefs = useRef({});
  const [heights, setHeights] = useState({});

  useEffect(() => {
    // Initialize heights for all answers
    const newHeights = {};
    productDetails?.faq?.forEach((_, idx) => {
      if (answerRefs.current[idx]) {
        newHeights[idx] = answerRefs.current[idx].scrollHeight;
      }
    });
    setHeights(newHeights);
  }, [productDetails?.faq]);

  const toggle = (idx) => {
    setOpenIndex(openIndex === idx ? null : idx);
  };

  return (
    <div className="bg-[#FAF9F5] p-6 md:p-10 rounded-2xl max-w-3xl">
      {/* Header */}
      <h2 className="text-2xl transformaSansBold text-black font-bold">FAQs</h2>
      <p className="text-gray-500 mt-1 Sora text-lg">
        Have a question? We’re here to help.
      </p>

      {/* FAQ List */}
      <div className="mt-6 space-y-1 Sora">
        {productDetails?.faq?.map((question, idx) => (
          <div key={idx} className="border-b overflow-hidden">
            <button
              onClick={() => toggle(idx)}
              className="flex justify-between items-center cursor-pointer w-full py-4 text-left text-base font-medium text-gray-900 hover:text-gray-700 transition-colors duration-300"
            >
              <span className={`${openIndex === idx ? 'text-black font-semibold' : ''} transition-colors duration-300`}>
                {question?.question}
              </span>
              <FiChevronDown
                className={`transform cursor-pointer transition-transform duration-300 ${
                  openIndex === idx ? "rotate-180" : ""
                }`}
                size={20}
              />
            </button>
            <div
              className="overflow-hidden transition-all duration-300 ease-in-out"
              style={{
                maxHeight: openIndex === idx ? `${heights[idx]}px` : '0px',
                opacity: openIndex === idx ? 1 : 0,
              }}
            >
              <div
                ref={el => answerRefs.current[idx] = el}
                className="pb-4 text-base text-gray-600"
              >
                {question?.answer}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
