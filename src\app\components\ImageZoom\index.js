"use client";
import React, { useState, useRef, useEffect } from "react";

const ImageZoom = ({ src, alt, className, zoomLevel = 250 }) => {
  const [isZoomed, setIsZoomed] = useState(false);
  const [coords, setCoords] = useState({ x: 50, y: 50 }); // Start in the center (50%)
  const containerRef = useRef(null);

  // Reset zoom when image changes
  useEffect(() => {
    setIsZoomed(false);
    setCoords({ x: 50, y: 50 });
  }, [src]);

  // Handle mouse movement
  const handleMouseMove = (e) => {
    if (!containerRef.current) return;

    const { left, top, width, height } = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - left) / width) * 100;
    const y = ((e.clientY - top) / height) * 100;

    // Ensure coordinates are within bounds
    const boundedX = Math.max(0, Math.min(100, x));
    const boundedY = Math.max(0, Math.min(100, y));

    setCoords({ x: boundedX, y: boundedY });
  };

  // Handle touch movement for mobile devices
  const handleTouchMove = (e) => {
    if (!containerRef.current || e.touches.length < 1) return;

    const touch = e.touches[0];
    const { left, top, width, height } = containerRef.current.getBoundingClientRect();
    const x = ((touch.clientX - left) / width) * 100;
    const y = ((touch.clientY - top) / height) * 100;

    // Ensure coordinates are within bounds
    const boundedX = Math.max(0, Math.min(100, x));
    const boundedY = Math.max(0, Math.min(100, y));

    setCoords({ x: boundedX, y: boundedY });
  };

  const handleTouchStart = (e) => {
    setIsZoomed(true);
    handleTouchMove(e);
  };

  const handleTouchEnd = () => {
    setIsZoomed(false);
  };

  return (
    <div
      className="relative w-full h-full overflow-hidden cursor-zoom-in"
      ref={containerRef}
      onMouseEnter={() => setIsZoomed(true)}
      onMouseLeave={() => setIsZoomed(false)}
      onMouseMove={handleMouseMove}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Original image */}
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-cover ${className || ''}`}
      />

      {/* Zoomed view */}
      {isZoomed && (
        <div className="absolute inset-0 pointer-events-none">
          <div
            className="absolute inset-0 z-10"
            style={{
              backgroundImage: `url(${src})`,
              backgroundPosition: `${coords.x}% ${coords.y}%`,
              backgroundSize: `${zoomLevel}%`,
              backgroundRepeat: 'no-repeat',
            }}
          />

          {/* Zoom indicator */}
          <div
            className="absolute w-12 h-12 border-2 border-white rounded-full z-20 opacity-50"
            style={{
              left: `calc(${coords.x}% - 24px)`,
              top: `calc(${coords.y}% - 24px)`,
              boxShadow: '0 0 0 1px rgba(0,0,0,0.3)',
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ImageZoom;
