import React, { useState, useEffect } from "react";
import { IoIosArrowDown } from "react-icons/io";
import { filter_categories } from "../../constants/filter_categories";
import Scrollbars from "react-custom-scrollbars";
import { useRouter } from "next/navigation";

const Sidebar = ({
  subcategories,
  categories = [],
  priceRanges = [],
  selectedFilters = {},
  toggleFilter,
  clearFilters,
  isFilterSelected,
  query = "",
  setQuery,
}) => {
  const router = useRouter();
  const [inputValue, setInputValue] = useState(query);
  const [openSections, setOpenSections] = useState(
    filter_categories.reduce((acc, section) => {
      acc[section.id] =
        section.id === "brands" ||
        section.id === "screensize" ||
        section.id === "Features"
          ? false
          : true;
      return acc;
    }, {})
  );

  // Add dynamic facets section
  const [dynamicFacets, setDynamicFacets] = useState([]);

  // Process categories and price ranges to create dynamic facets
  useEffect(() => {
    const processedFacets = [];

    // Process categories if available
    if (categories && categories.length > 0) {
      processedFacets.push({
        id: "dynamic_category",
        name: "Product Categories",
        type: "category_name", // Field name for filtering
        options: categories.map((category, index) => ({
          id: `cat_${index}`,
          value: category.name,
          label: category.name,
          count: 1, // We don't have actual counts, but we need a value > 0
        })),
      });
    }

    // Process price ranges if available
    if (priceRanges && priceRanges.length > 0) {
      // Include all price ranges, even those with zero count
      // This ensures price ranges are always visible
      processedFacets.push({
        id: "dynamic_price",
        name: "Price Range",
        type: "price", // Field name for filtering
        options: priceRanges.map((range, index) => ({
          id: `price_${index}`,
          value: range.value,
          // Use displayLabel if available (includes count), otherwise use label
          label: range.displayLabel || `${range.label} (${range.count || 0})`,
          count: range.count || 0,
        })),
      });
    }

    // Update dynamic facets
    setDynamicFacets(processedFacets);

    // Initialize openSections for dynamic facets
    setOpenSections((prev) => {
      const newOpenSections = { ...prev };
      processedFacets.forEach((facet) => {
        newOpenSections[facet.id] = true;
      });
      return newOpenSections;
    });
  }, [categories, priceRanges]);

  // Update inputValue when query prop changes
  useEffect(() => {
    setInputValue(query);
  }, [query]);

  // Handle filter selection
  const handleFilterSelect = (facetType, value) => {
    if (toggleFilter) {
      toggleFilter(facetType, value);
    }
  };

  const toggleSection = (id) => {
    setOpenSections((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleSubcategorySelect = (slug) => {
    router.push(`/${slug}`);
  };

  const allJobData = [
    { id: 1, name: "Software Engineer", status: "active" },
    { id: 2, name: "Product Manager", status: "inactive" },
    { id: 3, name: "UX Designer", status: "active" },
    { id: 4, name: "Data Analyst", status: "inactive" },
    { id: 5, name: "Marketing Specialist", status: "active" },
  ];

  // State to track expanded categories
  const [expandedCategories, setExpandedCategories] = useState({});

  // Function to toggle category expansion
  const toggleCategory = (slug) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [slug]: !prev[slug],
    }));
  };

  const renderTree = (node, handleClick, selectedSlug, selectedPath) => {
    const isExpanded =
      expandedCategories[node.slug] || selectedPath.includes(node.slug);
    const isSelected = selectedSlug === node.slug;
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div className="py-1" key={node.slug}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <label
              className="relative flex cursor-pointer Sora items-center rounded-full py-2 pr-3"
              htmlFor={`radio_${node.slug}`}
              data-ripple-dark="true"
            >
              <input
                id={`radio_${node.slug}`}
                type="checkbox"
                checked={isSelected}
                onChange={() => handleClick(node.slug)}
                className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
              />
              <div
                className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3.5 w-3.5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  stroke="currentColor"
                  strokeWidth="1"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>
            </label>
            <label
              className={`ml-0 text-sm ${
                isSelected ? "text-black" : "text-gray-400"
              } transformaSansNormal font-medium cursor-pointer`}
              htmlFor={`radio_${node.slug}`}
            >
              {node.name}
            </label>
          </div>

          {/* Add expand/collapse arrow for categories with children */}
          {hasChildren && (
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                toggleCategory(node.slug);
              }}
              className="p-1 focus:outline-none"
            ></button>
          )}
        </div>

        {/* Show children if expanded */}
        {node.children && isExpanded && (
          <div className="ml-4 pl-2">
            {node.children.map((child) =>
              renderTree(child, handleClick, selectedSlug, selectedPath)
            )}
          </div>
        )}
      </div>
    );
  };

  // Render dynamic filter options
  const renderDynamicFilterOptions = (section) => {
    if (!section.options || section.options.length === 0) {
      return (
        <div className="text-gray-500 text-sm py-2">No options available</div>
      );
    }

    return (
      <div className="h-auto overflow-hidden enable-scrollbar2 relative">
        {section.options.map((option) => (
          <div key={option.id} className="flex items-center">
            <div className="flex items-center">
              <label
                className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                htmlFor={`filter_${section.id}_${option.id}`}
                data-ripple-dark="true"
              >
                <input
                  id={`filter_${section.id}_${option.id}`}
                  type="checkbox"
                  className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                  checked={
                    isFilterSelected
                      ? isFilterSelected(section.type, option.value)
                      : false
                  }
                  onChange={() =>
                    handleFilterSelect(section.type, option.value)
                  }
                />
                <div
                  className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    stroke="currentColor"
                    strokeWidth="1"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                </div>
              </label>
              <label
                className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium cursor-pointer"
                htmlFor={`filter_${section.id}_${option.id}`}
              >
                {option.label || `${option.value} (${option.count || 0})`}
              </label>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Handle search form submission
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    // Only update the query state when the form is submitted
    setQuery(inputValue);
  };

  return (
    <div className="bg-white px-3 rounded-lg flex flex-col ">
      <div className="overflow-y-scroll border border-[#D9D9D999] p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <h1 className="transformaSansSemibold text-black">Filters</h1>
          <button
            onClick={clearFilters}
            className="text-gray-500 underline cursor-pointer"
          >
            Clear
          </button>
        </div>

        {/* Search input below the Filters heading */}
        {setQuery && (
          <div className="mt-4 mb-4">
            <form onSubmit={handleSearchSubmit}>
              <input
                id="sidebar-search"
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Search products..."
                className="w-full px-4 py-2 border border-gray-300 text-black Sora rounded-md focus:outline-none focus:ring-1 focus:ring-[#F0381A]"
              />
            </form>
          </div>
        )}

        <div className="px-2 mt-5">
          {/* Dynamic Facet Filters */}
          {dynamicFacets.map((section) => (
            <div key={section.id} className="border-b border-gray-300 py-6">
              <h3 className="-my-3 flow-root">
                <div
                  className="flex w-full items-center justify-between py-0 text-sm text-gray-400 transformaSansNormal hover:text-gray-500"
                  onClick={() => toggleSection(section.id)}
                >
                  <span className="font-bold text-sm transformaSansNormal text-gray-900">
                    {section.name}
                  </span>
                  <span className="ml-6 flex items-center">
                    <IoIosArrowDown
                      className={`h-5 w-5 ${
                        openSections[section.id] ? "transform rotate-180" : ""
                      }`}
                      aria-hidden="true"
                    />
                  </span>
                </div>
              </h3>
              {openSections[section.id] && (
                <div className="pt-6">
                  <div className="space-y-2">
                    {renderDynamicFilterOptions(section)}
                  </div>
                </div>
              )}
            </div>
          ))}

          {dynamicFacets?.length === 0 && (
            <>
              {/* Original Filter Categories */}
              {filter_categories.map((section) => (
                <div key={section.id} className="border-b border-gray-300 py-6">
                  <h3 className="-my-3 flow-root">
                    <div
                      className="flex w-full items-center justify-between py-0 text-sm text-gray-400 transformaSansNormal`1 hover:text-gray-500"
                      onClick={() => toggleSection(section.id)}
                    >
                      <span className="font-bold text-sm transformaSansNormal text-gray-900">
                        {section.id === "categories" ? (
                          <div className="flex flex-col gap-2">Category:</div>
                        ) : (
                          section.name
                        )}{" "}
                        {/* Changed to original category name */}
                      </span>
                      <span className="ml-6 flex items-center">
                        <IoIosArrowDown
                          className={`h-5 w-5 ${
                            openSections[section.id]
                              ? "transform rotate-180"
                              : ""
                          }`}
                          aria-hidden="true"
                        />
                      </span>
                    </div>
                  </h3>
                  {openSections[section.id] && (
                    <div className="pt-6">
                      <div className="space-y-2">
                        {section.id === "categories" && (
                          <>
                            <div className="text-sm ">
                              {renderTree(
                                subcategories.tree,
                                handleSubcategorySelect,
                                subcategories.selectedSlug,
                                subcategories.selectedPath
                              )}
                            </div>
                          </>
                        )}
                        {section?.id === "availability" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "pricerange" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "primarycamera" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "ram" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "memory" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "brands" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "screensize" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "Features" && (
                          <>
                            <div className="h-auto overflow-hidden enable-scrollbar2 relative">
                              {section?.options?.map((i) => (
                                <div key={i.id} className="flex items-center">
                                  <label
                                    className="relative flex cursor-pointer items-center rounded-full py-2 pr-3"
                                    htmlFor={`radio_${i.id}`}
                                    data-ripple-dark="true"
                                  >
                                    <input
                                      id="ripple-on"
                                      type="checkbox"
                                      className={`peer relative h-4 w-4 cursor-pointer checked:bg-[#F0381A] appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 checked:border-[#F0381A]  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                    />
                                    <div
                                      className={`pointer-events-none absolute opacity-0 ml-[1px] transition-opacity peer-checked:opacity-100`}
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3.5 w-3.5"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        stroke="currentColor"
                                        strokeWidth="1"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        ></path>
                                      </svg>
                                    </div>
                                  </label>
                                  <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                    {i.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                        {section?.id === "job_role" && (
                          <>
                            <div className="mb-4">
                              <input
                                label="Search"
                                className="bg-[#F0F0F0] w-full h-10 pl-4 pr-10 rounded-lg text-sm font-medium text-gray-400 transformaSansNormal`1 transformaSansNormal"
                              />
                            </div>
                            <div className="h-[20rem] overflow-hidden enable-scrollbar2 relative">
                              <Scrollbars
                                style={{
                                  width: "100%",
                                  height: "100%",
                                  overflowX: "hidden",
                                }}
                              >
                                {allJobData?.filter(
                                  (i) => i.status === "active"
                                ).length > 0 ? (
                                  allJobData?.map(
                                    (i) =>
                                      i?.status === "active" && (
                                        <div
                                          key={i.id}
                                          className="flex items-center"
                                        >
                                          <label
                                            className="relative flex cursor-pointer items-center rounded-full p-3"
                                            htmlFor={`radio_${i.id}`}
                                            data-ripple-dark="true"
                                          >
                                            <input
                                              id="ripple-on"
                                              type="checkbox"
                                              className={`peer relative h-4 w-4 cursor-pointer checked:bg-coalColor appearance-none rounded border-[2px] transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-8 before:w-8 border-gray-600  before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-coalColor  hover:before:opacity-50 `}
                                            />
                                            <div
                                              className={`pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 opacity-0 transition-opacity peer-checked:opacity-100`}
                                            >
                                              <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                className="h-3.5 w-3.5"
                                                viewBox="0 0 20 20"
                                                fill="currentColor"
                                                stroke="currentColor"
                                                strokeWidth="1"
                                              >
                                                <path
                                                  fillRule="evenodd"
                                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                  clipRule="evenodd"
                                                ></path>
                                              </svg>
                                            </div>
                                          </label>
                                          <label className="ml-0 text-sm peer-checked:text-black text-gray-400 transformaSansNormal font-medium">
                                            {i.name}
                                          </label>
                                        </div>
                                      )
                                  )
                                ) : (
                                  <div className="flex items-center justify-center h-full">
                                    <p className="text-sm text-gray-600 font-medium">
                                      No active jobs available
                                    </p>
                                  </div>
                                )}
                              </Scrollbars>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
