"use server";

import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { handleError } from "@/lib/common/error-handler";
import { successResponse, errorResponse } from "@/lib/common/responses";
import jwt from "jsonwebtoken";
import { setAuthCookie } from "@/lib/cookies";

const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret_here";
const JWT_EXPIRATION = "1d";
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;

// Define APIError type
interface APIError extends Error {
  status?: number;
}

interface GoogleUserData {
  email: string;
  given_name: string;
  family_name: string;
  picture?: string;
  sub: string; // Google's unique identifier
}

/**
 * Handle Google OAuth login
 * This function is called after Google OAuth flow is completed and we have user data
 * @param userData User data from Google OAuth
 */
export async function googleLogin(userData: GoogleUserData) {
  try {
    if (!userData || !userData.email) {
      return errorResponse("Invalid user data from Google", 400);
    }

    // Check if user exists in database
    let user = await db.query.users.findFirst({
      where: (u, { eq }) => eq(u.email, userData.email),
    });

    // If user doesn't exist, create a new one
    if (!user) {
      // Create a new user with Google data
      const result = await db
        .insert(users)
        .values({
          firstName: userData.given_name || "Google",
          lastName: userData.family_name || "User",
          email: userData.email,
          // Use a random password since the user will login with Google
          // This ensures the password field is filled but not usable for manual login
          password:
            Math.random().toString(36).slice(-10) + Date.now().toString(),
          role: "customer",
          isActive: true,
          loginMethod: "google", // Track that this user signed up with Google
        })
        .returning();

      if (!result || result.length === 0) {
        return errorResponse("Failed to create user", 500);
      }

      user = result[0];
    } else if (!user.isActive) {
      return errorResponse("Your account is inactive", 403);
    }

    // Generate JWT token with user data
    const tokenData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
    };

    const token = jwt.sign(tokenData, JWT_SECRET, {
      expiresIn: JWT_EXPIRATION,
    });

    console.log(token, "google auth login");
    // Set auth cookie
    await setAuthCookie(token);

    // Return success with user data
    return successResponse({ user: tokenData }, "Google login successful", 200);
  } catch (error) {
    return handleError(error as APIError);
  }
}

/**
 * Handle Google OAuth callback
 * This function is called by the client to exchange the authorization code for user data
 * @param code Authorization code from Google OAuth
 */
export async function handleGoogleCallback(code: string) {
  try {
    if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
      return errorResponse("Google OAuth is not configured", 500);
    }

    // Exchange code for tokens
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        code,
        client_id: GOOGLE_CLIENT_ID,
        client_secret: GOOGLE_CLIENT_SECRET,
        // Use the same redirect URI format as in the auth route
        redirect_uri: "http://localhost:3000/api/auth/google/callback",
        grant_type: "authorization_code",
      }),
    });

    const tokens = await tokenResponse.json();

    if (!tokens || tokens.error) {
      console.error("Error exchanging code for tokens:", tokens);
      return errorResponse("Failed to authenticate with Google", 401);
    }

    // Get user info with access token
    const userInfoResponse = await fetch(
      "https://www.googleapis.com/oauth2/v3/userinfo",
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      }
    );

    const googleUser = await userInfoResponse.json();

    if (!googleUser || !googleUser.email) {
      return errorResponse("Failed to get user info from Google", 401);
    }

    // Process the Google user data
    return await googleLogin({
      email: googleUser.email,
      given_name: googleUser.given_name,
      family_name: googleUser.family_name,
      picture: googleUser.picture,
      sub: googleUser.sub,
    });
  } catch (error) {
    console.error("Google callback error:", error);
    return handleError(error as APIError);
  }
}
