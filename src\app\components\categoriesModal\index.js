"use client";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { ToastContainer, Zoom } from "react-toastify";
import useOutsideClick from "@/app/utils/OutsideClick";
import closeIcon from "@/app/assets/images/closeIcon.png";
import { useRouter } from "next/navigation";

const CategoriesModal = ({ setCategoryDropdown, categoriesParent }) => {
  const categoriesRef = useRef(null);
  const router = useRouter();
  useOutsideClick(
    [
      {
        ref: categoriesRef,
        excludeClasses: [".categoriesClass"],
        excludeIds: ["categoryID"],
      },
    ],
    (ref, event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        setCategoryDropdown(false);
      }
    }
  );

  const [activeCategory, setActiveCategory] = useState(
    categoriesParent?.[0] || null
  );

  const handleSubcategorySelect = (slug) => {
    setCategoryDropdown(false);
    router.push(`/${slug}`);
  };

  useEffect(() => {
    if (!activeCategory && categoriesParent?.length > 0) {
      setActiveCategory(categoriesParent[0]);
    }
  }, [categoriesParent]);

  return (
    <>
      <ToastContainer
        position="bottom-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <div
        id="categoryID"
        className="categoriesClass container mx-auto  absolute z-40 border border-black Sora left-0 right-0 top-20 h-auto overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] w-full"
        ref={categoriesRef}
      >
        <div className="p-10 bg-[#F0EFEF]">
          <Image
            src={closeIcon}
            height={35}
            width={35}
            alt="close-icon"
            className="cursor-pointer"
            onClick={() => setCategoryDropdown(false)}
          />
          <div className="grid grid-cols-12 gap-6 mt-8">
            {/* Sidebar - Parent Categories */}
            <div className="col-span-3 flex flex-col text-black space-y-3 text-base">
              {categoriesParent?.map((cat) => (
                <span
                  key={cat.id}
                  className={`cursor-pointer ${
                    activeCategory?.id === cat.id
                      ? "text-[#F0381A] font-semibold"
                      : "hover:text-[#F0381A]"
                  }`}
                  onClick={() => {
                    setActiveCategory(cat);
                    handleSubmit(cat.slug);
                  }}
                >
                  {cat.name}
                </span>
              ))}
            </div>

            {/* Subcategories & Children */}
            <div
              className={`col-span-9 grid grid-cols-4 gap-x-20 gap-y-8 max-w-full`}
            >
              {activeCategory?.subcategories?.map((sub) => (
                <div
                  key={sub.id}
                  className="text-base text-black cursor-pointer space-y-1"
                >
                  <p
                    className="font-semibold truncate mb-1"
                    onClick={() => {
                      handleSubcategorySelect(sub.slug);
                    }}
                  >
                    {sub.name}
                  </p>

                  {/* Render sub-subcategories if available */}
                  {sub.subcategories?.length > 0 && (
                    <div className="space-y-1">
                      {sub.subcategories.map((child) => (
                        <span
                          key={child.id}
                          className="text-gray-700 hover:text-black block truncate cursor-pointer"
                          onClick={() => {
                            handleSubcategorySelect(child.slug);
                          }}
                        >
                          {child.name}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CategoriesModal;
