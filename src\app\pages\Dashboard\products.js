"use client";
import React from "react";
import { FaHeart } from "react-icons/fa";
import { products } from "../../constants/products";
import { toggleProductFavorite } from "../../utils/favoriteActions";
import { renderStars } from "../../utils/renderStars";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useFavorites } from "../../hooks/useFavorites";

export const Cards = () => {
  const { favorites, setFavorites, loading: favoritesLoading } = useFavorites();
  const router = useRouter();

  const handleNavigation = () => {
    router.push(`/pages/product`);
  };

  return (
    <>
      {products.map((product) => (
        <div
          key={product.id}
          className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300 cursor-pointer"
          onClick={() => handleNavigation(product.id)}
        >
          <div className="relative">
            {product.isBestProduct && (
              <span className="absolute top-2 left-2 bg-white text-black px-2 py-1 rounded-full text-xs transformaSansNormal">
                Best Product
              </span>
            )}
            {favoritesLoading ? (
              <div className="absolute top-2 right-2 p-2 bg-white/80 rounded-full">
                <div className="animate-pulse w-5 h-5 rounded-full bg-gray-300"></div>
              </div>
            ) : (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  toggleProductFavorite(product.id, setFavorites);
                }}
                className="absolute top-2 right-2 p-2 bg-white/80 rounded-full"
              >
                <FaHeart
                  className={
                    favorites.has(product.id) ? "text-red-500" : "text-gray-400"
                  }
                />
              </button>
            )}
            <div className="bg-[#FBF7F7] p-14">
              <Image
                src={product.image}
                alt={product.name}
                className="w-full h-40 object-contain"
              />
            </div>
          </div>
          <div className="p-4">
            <div className="justify-between flex">
              <h3 className="text-xl font-semibold text-gray-900 transformaSansSemibold">
                {product.name}
              </h3>
              <div className="flex items-center mb-2">
                <div className="flex mr-2">{renderStars(product.rating)}</div>
                <span className="text-sm text-gray-600">{product.rating}</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-lg font-normal text-red-500 transformaSansNormal">
                ${product.price}
              </span>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};
