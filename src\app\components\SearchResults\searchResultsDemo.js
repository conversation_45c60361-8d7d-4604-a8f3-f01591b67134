"use client";

import React from "react";
import Image from "next/image";
import { IoSearchOutline } from "react-icons/io5";
import { TailSpin } from "react-loader-spinner";

const SearchResultsDemo = ({
  searchResults,
  isLoading,
  isError,
  handleResultClick,
  searchTerm,
  searchType = "text",
}) => {
  //#region Loading when you search something
  if (isLoading) {
    return (
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-4">
        <div className="flex justify-center items-center py-4">
          <TailSpin
            height="30"
            width="30"
            color="#E0482F"
            ariaLabel="loading"
          />
        </div>
      </div>
    );
  }
  //#endregion

  //#region Error states
  if (isError) {
    const errorMessage =
      searchResults?.message ||
      "Error loading search results. Please try again.";
    const errorType = searchResults?.error;
    const errorDetails = searchResults?.details;
    return (
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-4">
        <p className="text-red-500 text-center py-2">{errorMessage}</p>
        {errorType === "CONNECTION_ERROR" && (
          <p className="text-gray-600 text-sm text-center">
            Please make sure Typesense Docker container is running. Try running:
            <br />
            <code className="bg-gray-100 px-1">docker-compose up -d</code>
          </p>
        )}
        {errorType === "COLLECTION_NOT_FOUND" && (
          <p className="text-gray-600 text-sm text-center">
            Run <code className="bg-gray-100 px-1">yarn typesense:sync</code> to
            import products.
          </p>
        )}
        {errorType === "INVALID_QUERY" && (
          <p className="text-gray-600 text-sm text-center">
            Try simplifying your search query or using different keywords.
          </p>
        )}
        {errorType === "SEARCH_ERROR" && (
          <p className="text-gray-600 text-sm text-center">
            An unexpected error occurred. Please try again with a different
            search term.
          </p>
        )}
      </div>
    );
  }
  //#endregion

  //#region no product found or invalid data
  if (
    !searchResults ||
    !searchResults.data ||
    !searchResults.data.hits ||
    searchResults.data.hits.length === 0
  ) {
    return (
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-4">
        <p className="text-gray-500 text-center py-2">
          {searchTerm && searchTerm.length > 1
            ? "No products found. Try a different search term."
            : "Type at least 2 characters to search"}
        </p>
      </div>
    );
  }
  //#endregion

  // Checking if the search term contains a model number pattern (like XD4)
  const modelNumberPattern = /\b[A-Za-z]{1,3}\d+\b/i;
  const containsModelNumber = searchTerm && modelNumberPattern.test(searchTerm);
  const modelMatch = containsModelNumber
    ? searchTerm.match(modelNumberPattern)[0]
    : null;

  // Checking if the exact model number is in any of the results
  const exactModelFound =
    modelMatch &&
    searchResults.data.hits.some(
      (hit) =>
        hit.document.name.includes(modelMatch) ||
        (hit.document.description &&
          hit.document.description.includes(modelMatch))
    );

  // Showing a warning banner if we're searching for a model number but didn't find exact matches
  const showSimilarModelWarning = containsModelNumber && !exactModelFound;

  return (
    <div className="bg-white shadow-lg rounded-lg border border-gray-200">
      {/* Search Type Indicator */}
      <div className="px-3 py-2 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-purple-200">
        <div className="flex items-center justify-between">
          <span className="text-xs text-purple-700 font-medium">
            Search powered by:
          </span>
          <div className="flex items-center gap-2">
            <span className="text-xs px-2 py-1 rounded-full font-medium bg-purple-100 text-purple-600 border border-purple-200">
              🧠 AI Vector Search
            </span>
            {/* Show actual search metadata */}
            {searchResults?.data?.search_metadata && (
              <span className="text-xs text-purple-600 italic">
                ({searchResults.data.search_metadata.type.replace("_", " ")})
              </span>
            )}
          </div>
        </div>
      </div>

      {showSimilarModelWarning && (
        <div className="border-l-4 border-yellow-400 bg-yellow-50 p-3 m-3">
          <p className="text-sm font-medium text-yellow-800">
            We couldn't find exact matches for "{modelMatch}"
          </p>
          <p className="text-xs text-yellow-700 mt-1">
            Showing similar products that might interest you
          </p>
        </div>
      )}

      <ul className="divide-y divide-gray-100">
        {searchResults.data.hits.map((hit) => (
          <li
            key={hit.document.id}
            className="p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
            onClick={() => handleResultClick(hit.document.name)}
          >
            <div className="flex items-center space-x-3">
              {hit.document.featuredImage ? (
                <div className="flex-shrink-0 h-10 w-10 relative">
                  <Image
                    src={hit.document.featuredImage}
                    alt={hit.document.name}
                    fill
                    className="object-cover rounded"
                  />
                </div>
              ) : (
                <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
                  <IoSearchOutline className="text-gray-500" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {hit.document.name}
                </p>
                <p className="text-sm text-gray-500 truncate">
                  {hit.document.description
                    ? hit.document.description.substring(0, 60) +
                      (hit.document.description.length > 60 ? "..." : "")
                    : ""}
                </p>
              </div>
              <div className="flex-shrink-0 text-sm font-medium text-[#E0482F]">
                ${hit.document.price.toFixed(2)}
              </div>
            </div>
          </li>
        ))}
      </ul>
      {searchTerm && (
        <div className="p-3 bg-gray-50 border-t border-gray-100">
          <button
            className="w-full text-center text-sm cursor-pointer text-[#E0482F] hover:underline"
            onClick={() => handleResultClick(searchTerm)}
          >
            View all results for "{searchTerm}"
          </button>
        </div>
      )}
    </div>
  );
};

export default SearchResultsDemo;
