"use client";
import React from "react";
import fashion from "../../../assets/images/fashion.png";
import PopularProducts from "@/app/components/popular";
import EidBanner from "@/app/components/EidBanner";
import Fire from "@/app/assets/images/fire.png";
// import { useRouter } from "next/navigation";
import Image from "next/image";

const BestWeek2 = () => {
  // const router = useRouter();

  const handleNavigation = () => {
    // router.push(`/categories`);
  };
  return (
    <div className="py-5">
      <div className="container mx-auto px-4">
        <div className=" grid grid-cols-12 gap-10">
          <div className="mt-14 col-span-5">
            <EidBanner
              background="bg-[#15361B]"
              headingColor="text-white"
              subheadingColor="text-white"
              buttonColor="text-white"
              buttonBorderColor="text-white"
              image={fashion}
              heading="Fashion"
              onClick={() => handleNavigation()}
            />
          </div>
          <div className="col-span-7">
            <div className="flex flex-row mb-6 gap-5">
              <h2 className="text-2xl font-bold transformaSansSemibold text-black  text-left">
                Best of this week
              </h2>
              <Image
                src={Fire}
                alt="fire"
                className="object-contain"
                height={20}
                width={20}
              />
            </div>
            <PopularProducts slidePercentage={33.33} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BestWeek2;
