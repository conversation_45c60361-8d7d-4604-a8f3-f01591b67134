"use client";
import React, { useState } from "react";
import GlobalDropdown from "./GlobalDropdown";
import DropdownItem from "./DropdownItem";
import { FaUser, FaCog, FaShoppingCart, FaSignOutAlt } from "react-icons/fa";

const DropdownExample = () => {
  // Example 1: Basic dropdown with items
  const [isOpen1, setIsOpen1] = useState(false);
  
  // Example 2: Custom trigger and styling
  const [isOpen2, setIsOpen2] = useState(false);
  
  // Example 3: Dropdown with internal state management
  
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold mb-4">Global Dropdown Examples</h1>
      
      {/* Example 1: Basic dropdown */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Basic Dropdown</h2>
        <GlobalDropdown
          isOpen={isOpen1}
          setIsOpen={setIsOpen1}
          trigger={
            <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
              Toggle Dropdown
            </button>
          }
          width="w-48"
        >
          <ul>
            <DropdownItem onClick={() => console.log("Item 1 clicked")}>
              Menu Item 1
            </DropdownItem>
            <DropdownItem onClick={() => console.log("Item 2 clicked")}>
              Menu Item 2
            </DropdownItem>
            <DropdownItem onClick={() => console.log("Item 3 clicked")}>
              Menu Item 3
            </DropdownItem>
          </ul>
        </GlobalDropdown>
      </div>
      
      {/* Example 2: Custom styled dropdown */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Custom Styled Dropdown</h2>
        <GlobalDropdown
          isOpen={isOpen2}
          setIsOpen={setIsOpen2}
          trigger={
            <button className="px-4 py-2 bg-gray-800 text-white rounded-full flex items-center gap-2">
              <span>User Menu</span>
              <FaUser />
            </button>
          }
          width="w-56"
          position="left-0 top-full"
          background="bg-gray-800"
          border="border-0"
          rounded="rounded-md"
          shadow="shadow-lg"
          className="mt-2 text-white"
        >
          <ul className="py-2">
            <DropdownItem 
              icon={<FaUser />} 
              onClick={() => console.log("Profile clicked")}
              hoverEffect="hover:bg-gray-700"
              className="text-white"
            >
              Profile
            </DropdownItem>
            <DropdownItem 
              icon={<FaCog />} 
              onClick={() => console.log("Settings clicked")}
              hoverEffect="hover:bg-gray-700"
              className="text-white"
            >
              Settings
            </DropdownItem>
            <DropdownItem 
              icon={<FaShoppingCart />} 
              onClick={() => console.log("Orders clicked")}
              hoverEffect="hover:bg-gray-700"
              className="text-white"
            >
              My Orders
            </DropdownItem>
            <hr className="my-2 border-gray-600" />
            <DropdownItem 
              icon={<FaSignOutAlt />} 
              onClick={() => console.log("Logout clicked")}
              hoverEffect="hover:bg-gray-700"
              className="text-white"
            >
              Logout
            </DropdownItem>
          </ul>
        </GlobalDropdown>
      </div>
      
      {/* Example 3: Dropdown with internal state management */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Dropdown with Internal State</h2>
        <GlobalDropdown
          trigger={
            <button className="px-4 py-2 border border-gray-300 rounded-md">
              Click Me
            </button>
          }
          width="w-48"
        >
          <div className="p-4">
            <p>This dropdown manages its own state internally.</p>
            <button 
              className="mt-2 px-3 py-1 bg-red-500 text-white rounded-md text-sm"
              onClick={() => console.log("Action button clicked")}
            >
              Action
            </button>
          </div>
        </GlobalDropdown>
      </div>
    </div>
  );
};

export default DropdownExample;
