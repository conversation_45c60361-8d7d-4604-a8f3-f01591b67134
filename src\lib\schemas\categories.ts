import {
  pgTable,
  serial,
  text,
  boolean,
  timestamp,
  integer,
} from "drizzle-orm/pg-core";

export const categories = pgTable("category", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  image: text("image"),
  hawkeyeId: text("hawkeye_id").notNull(),
  isPublic: boolean("isPublic").default(true),
  showInNav: boolean("showInNav").default(false),
  status: text("status").notNull().default("active"),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
  deletedAt: timestamp("deletedAt"),
  parentId: integer("parentId"),
});
