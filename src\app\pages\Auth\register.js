import React, { useState, useRef } from "react";
import { features } from "../../constants/features";
import { registerUser } from "@/server/auth/index";
import { useFormik } from "formik";
import checkmark from "../../assets/images/red_checkmark.png";
import Image from "next/image";
import TextField from "@/app/components/TextField";
import CustomButton from "@/app/components/button";
import Google from "../../assets/images/google.png";
import Apple from "../../assets/images/apple_logo.png";
import Facebook from "../../assets/images/fb.png";
import * as Yup from "yup";
import { toast } from "react-toastify";
import useOutsideClick from "@/app/utils/OutsideClick";

const Register = ({ setUserdropdown, setShowRegister }) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const loginRef = useRef(null);

  //#region Formik validations
  const validation = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstname: "",
      lastname: "",
      email: "",
      password: "",
    },
    validationSchema: Yup.object({
      firstname: Yup.string().required("First Name Required"),
      lastname: Yup.string().required("Last Name Required"),
      email: Yup.string().required("Email Required"),
      password: Yup.string().required("Password Required"),
    }),
    onSubmit: (values) => {
      handleRegister(values);
    },
  });
  //#endregion

  //#region Calling service function to register user
  async function handleRegister(values) {
    try {
      const response = await registerUser(
        values?.firstname,
        values?.lastname,
        values?.email,
        values?.password
      );
      if (response?.statusCode === 201) {
        toast.success("User registered successfully", {
          style: { width: "450px" },
        });
      } else {
        toast.error(response?.message, {
          style: { width: "450px" },
        });
      }
    } catch (error) {
      console.log("Unexpected error:", error.message);
    }
  }
  //#endregion

  //#region Outside click to close modal
  useOutsideClick(
    [
      {
        ref: loginRef,
        excludeClasses: [".loginClass"],
        excludeIds: ["login"],
      },
    ],
    (ref) => {
      if (ref === loginRef) {
        setUserdropdown(false);
        setShowRegister(false);
      }
    }
  );
  //#endregion

  return (
    <>
      <div
        id="login"
        className="loginClass absolute z-[9999] border border-coalColor right-0 top-full h-auto overflow-scroll bg-white rounded-lg shadow-[0_3px_10px_rgb(0,0,0,0.2)] 2xl:w-[853px] lg:w-[700px]"
        ref={loginRef}
      >
        <div className="grid grid-cols-2 gap-5">
          {/* 🔹 Grid Column 1 */}
          <div className="p-5 bg-[#F0EFEF]">
            <h1 className="transformaSansSemibold">The Pricio Account</h1>
            <p className="text-gray-600 text-base mt-4 transformaSansNormal">
              With the pricio account, we aim to give you the best possible
              price comparison experience.
            </p>
            <div className="mt-5">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3 mb-4">
                  <div>
                    <div className="flex flex-row space-x-3">
                      <Image
                        src={checkmark}
                        alt="checkmark red"
                        className="object-contain"
                        height={20}
                        width={20}
                      />
                      <h3 className="font-semibold text-lg transformaSansSemibold">
                        {feature.title}
                      </h3>
                    </div>
                    <p className="text-gray-600 text-sm ml-8 transformaSansNormal">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* 🔹 Grid Column 2 */}
          <div className="p-5">
            <h1 className="transformaSansSemibold">Create an account</h1>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                validation.handleSubmit();
                return false;
              }}
            >
              <div className="mt-4 space-y-5 transformaSansSemibold">
                <div className="grid grid-cols-2 h-14 gap-5">
                  <div>
                    <TextField
                      type="text"
                      name="firstname"
                      id="firstname"
                      customPlaceholder="First name"
                      customWidth="w-full"
                      customBorder="border border-gray-400 focus:border-black focus:shadow-sm"
                      customPadding="py-3 px-4"
                      iconBackground="bg-[#ff385c]"
                      iconColor="text-white"
                      iconDimensions="w-8 h-8"
                      iconPadding="p-2"
                      value={validation.values.firstname || ""}
                      onChange={validation.handleChange}
                      onBlur={validation.handleBlur}
                    />
                    {validation.touched.firstname &&
                    validation.errors.firstname ? (
                      <div className="ml-1 fade-in-text-validations">
                        <p className="text-rose-500 text-xs transformaSansNormal">
                          {validation.errors.firstname}
                        </p>
                      </div>
                    ) : null}
                  </div>
                  <div>
                    <TextField
                      type="text"
                      name="lastname"
                      id="lastname"
                      customPlaceholder="Last name"
                      customWidth="w-full"
                      customBorder="border border-gray-400 focus:border-black focus:shadow-sm"
                      customPadding="py-3 px-4"
                      iconBackground="bg-[#ff385c]"
                      iconColor="text-white"
                      iconDimensions="w-8 h-8"
                      iconPadding="p-2"
                      value={validation.values.lastname || ""}
                      onChange={validation.handleChange}
                      onBlur={validation.handleBlur}
                    />
                    {validation.touched.lastname &&
                    validation.errors.lastname ? (
                      <div className="ml-1 fade-in-text-validations">
                        <p className="text-rose-500 text-xs transformaSansNormal">
                          {validation.errors.lastname}
                        </p>
                      </div>
                    ) : null}
                  </div>
                </div>
                <div className="h-14">
                  <TextField
                    type="email"
                    name="email"
                    id="email"
                    customPlaceholder="Email"
                    customWidth="w-full"
                    customBorder="border border-gray-400 focus:border-black focus:shadow-sm"
                    customPadding="py-3 px-4"
                    iconBackground="bg-[#ff385c]"
                    iconColor="text-white"
                    iconDimensions="w-8 h-8"
                    iconPadding="p-2"
                    value={validation.values.email || ""}
                    onChange={validation.handleChange}
                    onBlur={validation.handleBlur}
                  />
                  {validation.touched.email && validation.errors.email ? (
                    <div className="ml-1 fade-in-text-validations">
                      <p className="text-rose-500 text-xs transformaSansNormal">
                        {validation.errors.email}
                      </p>
                    </div>
                  ) : null}
                </div>
                <div className="h-14">
                  <TextField
                    type={isPasswordVisible ? "text" : "password"}
                    name="password"
                    id="password"
                    customPlaceholder="Password"
                    customWidth="w-full"
                    customBorder="border border-gray-400 focus:border-black focus:shadow-sm"
                    customPadding="py-3 px-4"
                    iconBackground="bg-[#ff385c]"
                    iconColor="text-white"
                    iconDimensions="w-8 h-8"
                    iconPadding="p-2"
                    isPasswordVisible={isPasswordVisible}
                    setIsPasswordVisible={setIsPasswordVisible}
                    passwordIcon={true}
                    value={validation.values.password || ""}
                    onChange={validation.handleChange}
                    onBlur={validation.handleBlur}
                  />
                  {validation.touched.password && validation.errors.password ? (
                    <div className="ml-1 fade-in-text-validations">
                      <p className="text-rose-500 text-xs transformaSansNormal">
                        {validation.errors.password}
                      </p>
                    </div>
                  ) : null}
                </div>
                <CustomButton
                  label="Continue with Email"
                  bgColor="bg-[#E0482F]"
                  textColor="text-white"
                  paddingY="py-3"
                  textSize="text-base"
                  buttonType="submit"
                />
                <p className="transformaSansNormal text-sm text-center text-black mt-2 cursor-pointer">
                  Already have an account?{" "}
                  <span
                    className="text-[#F0381A] transformaSansSemibold cursor-pointer"
                    onClick={() => {
                      setShowRegister(false);
                    }}
                  >
                    Sign in
                  </span>
                </p>
              </div>
            </form>
            <div className="my-4 flex flex-row gap-3 transformaSansNormal">
              <hr className="border border-gray-300 w-full my-auto" />
              <p className="mx-2 text-base my-auto text-gray-500">OR</p>
              <hr className="border border-gray-300 w-full my-auto" />
            </div>
            <div className="gap-5 flex flex-col">
              <CustomButton
                label="Continue with Google"
                bgColor="bg-white"
                textColor="text-[#2B2B2B80]"
                paddingY="py-3"
                textSize="text-base"
                borderCustom="border border-[#2B2B2B80]"
                imageLeft={Google}
                iconWidth={20}
                iconHeight={20}
                autoLeftMargin="mr-2"
                onClick={() => (window.location.href = "/api/auth/google")}
              />
              <CustomButton
                label="Continue with Apple"
                bgColor="bg-white"
                textColor="text-[#2B2B2B80]"
                paddingY="py-3"
                textSize="text-base"
                borderCustom="border border-[#2B2B2B80]"
                imageLeft={Apple}
                iconWidth={20}
                iconHeight={20}
                autoLeftMargin="mr-2"
              />
              <CustomButton
                label="Continue with Facebook"
                bgColor="bg-white"
                textColor="text-[#2B2B2B80]"
                paddingY="py-3"
                textSize="text-base"
                borderCustom="border border-[#2B2B2B80]"
                imageLeft={Facebook}
                iconWidth={20}
                iconHeight={20}
                autoLeftMargin="mr-2"
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Register;
