"use server";

import typesenseServerClient from "@/lib/typesense-server";
import { generateEmbedding } from "@/lib/embeddings-server";

export async function vectorSearchProducts(query, options = {}) {
  try {
    if (!query || query.trim() === "") {
      return { success: true, data: { hits: [] } };
    }

    const {
      limit = 10,
      page = 1,
      perPage = 10,
      includeFields,
      filterBy = "",
      k = 50,
    } = options;

    // Generate embedding for the search query
    console.log("Generating embedding for query:", query);
    let queryEmbedding;

    try {
      queryEmbedding = await generateEmbedding(query);

      // Validate embedding
      if (
        !queryEmbedding ||
        !Array.isArray(queryEmbedding) ||
        queryEmbedding.length === 0
      ) {
        console.error("Invalid embedding generated:", queryEmbedding);
        throw new Error("Invalid embedding generated");
      }
    } catch (embeddingError) {
      console.error("Failed to generate embedding:", embeddingError);
      // Fall back to text search
      console.log("Falling back to text search due to embedding failure");
      const { searchProducts } = await import("./search.js");
      return await searchProducts(query, { ...options, searchType: "text" });
    }

    // Validate embedding dimensions match the collection schema (384 dimensions)
    if (queryEmbedding.length !== 384) {
      console.error(
        `Query embedding has ${queryEmbedding.length} dimensions, but collection expects 384 dimensions`
      );
      const { searchProducts } = await import("./search.js");
      return await searchProducts(query, {
        ...options,
        searchType: "text",
      });
    }

    console.log(
      `Using full 384-dimensional embedding for vector search (${queryEmbedding.length} dimensions)`
    );

    // Use multi-search endpoint to handle large vector queries
    const searchRequest = {
      searches: [
        {
          collection: "products",
          q: "*", // Use wildcard for vector search
          vector_query: `embedding:(${queryEmbedding.join(",")}, k:${k})`,
          page,
          per_page: perPage,

          // Include category fields in the response
          include_fields:
            includeFields ||
            "id,name,description,price,priceOld,featuredImage,keywords,standardizedTitle,category_id,category_name,category_slug",
        },
      ],
    };

    // Add filter_by if provided
    if (filterBy) {
      searchRequest.searches[0].filter_by = filterBy;
    }

    try {
      const multiSearchResults =
        await typesenseServerClient.multiSearch.perform(searchRequest);

      // Validate multi-search response
      if (
        !multiSearchResults ||
        !multiSearchResults.results ||
        !Array.isArray(multiSearchResults.results)
      ) {
        console.error("Invalid multi-search response:", multiSearchResults);
        return {
          success: false,
          message: "Invalid response from search server",
          error: "INVALID_RESPONSE",
        };
      }

      // Extract the first (and only) search result
      const searchResults = multiSearchResults.results[0];

      // Validate search results
      if (!searchResults) {
        console.error("No search results in multi-search response");
        return {
          success: false,
          message: "No search results returned",
          error: "NO_RESULTS",
        };
      }

      // Log search results for debugging
      if (searchResults.found && searchResults.found > 0) {
        console.log(
          `Vector search found ${searchResults.found} results for query: "${query}"`
        );
        if (searchResults.hits && searchResults.hits.length > 0) {
          console.log(
            "First vector hit:",
            searchResults.hits[0]?.document?.name || "Unknown"
          );
        }
      } else {
        console.log(`No vector search results found for query: "${query}"`);
        // Fall back to text search but mark it as vector fallback
        console.log("Falling back to text search for vector query");
        const { searchProducts } = await import("./search.js");
        const fallbackResults = await searchProducts(query, {
          ...options,
          searchType: "text",
        });

        if (fallbackResults.success) {
          // Modify results to show they're from vector search (fallback)
          return {
            ...fallbackResults,
            searchType: "vector",
            fallback: true,
            data: {
              ...fallbackResults.data,
              // Add a note to distinguish vector results
              search_metadata: {
                type: "vector_fallback",
                note: "Vector search with text fallback",
              },
            },
          };
        }
      }

      return {
        success: true,
        data: searchResults,
        searchType: "vector",
      };
    } catch (searchError) {
      console.error("Typesense vector search error:", searchError);

      // Handle specific error types
      if (searchError.code === "ECONNREFUSED") {
        return {
          success: false,
          message:
            "Could not connect to Typesense server. Please make sure Typesense is running.",
          error: "CONNECTION_ERROR",
        };
      }

      if (
        searchError.httpStatus === 404 &&
        searchError.message.includes("Collection not found")
      ) {
        return {
          success: false,
          message:
            "Products collection not found. Please run the data sync first.",
          error: "COLLECTION_NOT_FOUND",
        };
      }

      if (searchError.httpStatus === 400) {
        return {
          success: false,
          message:
            "Invalid vector search query format. Please try a different search term.",
          error: "INVALID_QUERY",
          details: searchError.message,
        };
      }

      // Generic error handler
      return {
        success: false,
        message: "An error occurred during vector search. Please try again.",
        error: "VECTOR_SEARCH_ERROR",
        details: searchError.message,
      };
    }
  } catch (error) {
    console.error("Error in vector search:", error);
    return {
      success: false,
      message: "Failed to perform vector search",
      error: error.message,
    };
  }
}

/**
 * Perform hybrid search combining text and vector search
 * @param {string} query - The search query
 * @param {Object} options - Search options
 * @returns {Promise<Object>} - Combined search results
 */
export async function hybridSearchProducts(query, options = {}) {
  try {
    if (!query || query.trim() === "") {
      return { success: true, data: { hits: [] } };
    }

    const {
      limit = 10,
      page = 1,
      perPage = 10,
      includeFields,
      filterBy = "",
      textWeight = 0.7,
      vectorWeight = 0.3,
    } = options;

    // Import the regular text search function
    const { searchProducts } = await import("./search.js");

    // Perform both text and vector searches in parallel
    const [textResults, vectorResults] = await Promise.all([
      searchProducts(query, {
        limit: Math.ceil(perPage * 1.5), // Get more results for merging
        page: 1, // Always start from page 1 for merging
        perPage: Math.ceil(perPage * 1.5),
        includeFields,
        filterBy,
      }),
      vectorSearchProducts(query, {
        limit: Math.ceil(perPage * 1.5),
        page: 1,
        perPage: Math.ceil(perPage * 1.5),
        includeFields,
        filterBy,
      }),
    ]);

    // Check if both searches were successful
    if (!textResults.success || !vectorResults.success) {
      // If one fails, return the successful one
      if (textResults.success)
        return { ...textResults, searchType: "text_fallback" };
      if (vectorResults.success)
        return { ...vectorResults, searchType: "vector_fallback" };

      // If both fail, return the text search error
      return textResults;
    }

    // Merge and rank results
    const mergedResults = mergeSearchResults(
      textResults.data,
      vectorResults.data,
      textWeight,
      vectorWeight
    );

    // Apply pagination to merged results
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedHits = mergedResults.hits.slice(startIndex, endIndex);

    return {
      success: true,
      data: {
        ...mergedResults,
        hits: paginatedHits,
        page,
        per_page: perPage,
        search_metadata: {
          type: "hybrid_search",
          note: "Combined text + vector search results",
          text_weight: textWeight,
          vector_weight: vectorWeight,
        },
      },
      searchType: "hybrid",
    };
  } catch (error) {
    console.error("Error in hybrid search:", error);
    return {
      success: false,
      message: "Failed to perform hybrid search",
      error: error.message,
    };
  }
}

/**
 * Merge text and vector search results with weighted scoring
 * @param {Object} textResults - Text search results
 * @param {Object} vectorResults - Vector search results
 * @param {number} textWeight - Weight for text search scores
 * @param {number} vectorWeight - Weight for vector search scores
 * @returns {Object} - Merged results
 */
function mergeSearchResults(
  textResults,
  vectorResults,
  textWeight,
  vectorWeight
) {
  const resultMap = new Map();

  // Process text search results
  if (textResults.hits) {
    textResults.hits.forEach((hit, index) => {
      const id = hit.document.id;
      const textScore = 1 / (index + 1); // Simple ranking score

      resultMap.set(id, {
        document: hit.document,
        textScore,
        vectorScore: 0,
        combinedScore: textScore * textWeight,
        highlight: hit.highlight || {},
      });
    });
  }

  // Process vector search results
  if (vectorResults.hits) {
    vectorResults.hits.forEach((hit, index) => {
      const id = hit.document.id;
      const vectorScore = 1 / (index + 1); // Simple ranking score

      if (resultMap.has(id)) {
        // Update existing result
        const existing = resultMap.get(id);
        existing.vectorScore = vectorScore;
        existing.combinedScore =
          existing.textScore * textWeight + vectorScore * vectorWeight;
      } else {
        // Add new result
        resultMap.set(id, {
          document: hit.document,
          textScore: 0,
          vectorScore,
          combinedScore: vectorScore * vectorWeight,
          highlight: {},
        });
      }
    });
  }

  // Sort by combined score and convert to array
  const sortedResults = Array.from(resultMap.values()).sort(
    (a, b) => b.combinedScore - a.combinedScore
  );

  return {
    found: sortedResults.length,
    hits: sortedResults.map((result) => ({
      document: result.document,
      highlight: result.highlight,
      text_match_info: {
        score: result.combinedScore,
        text_score: result.textScore,
        vector_score: result.vectorScore,
      },
    })),
  };
}
