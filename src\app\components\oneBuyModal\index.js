import React from "react";
import Image from "next/image";
import { FaStar } from "react-icons/fa";
import closeIcon from "../../assets/images/closeIcon.png";

const OneBuyModal = ({ setOneBuyModal }) => {
  const paymentMethods = [
    {
      name: "Sofort",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745320497/pricio/jvpxlukn1wn3yzv5fnn1.png",
    },
    {
      name: "Mastercard",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745320639/pricio/nac9kbynyqg07d2uc7mq.png",
    },
    {
      name: "Visa",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745320706/pricio/uf8jslajcry1y3h9m28h.png",
    },
    {
      name: "Google Pay",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745320739/pricio/l2z2jwbijt1ohrqxfmbo.png",
    },
    {
      name: "Mastercard",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745320779/pricio/fn1oa67ahfcqtn90jng1.png",
    },
  ];

  // Delivery service icons
  const deliveryServices = [
    {
      name: "M&P",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745321786/pricio/a009fa3zys3q8nvxbpht.png",
    },
    {
      name: "DHL",
      src: "https://res.cloudinary.com/comsats-software-solutions/image/upload/v1745321786/pricio/vh2qgkitpsv4uafghkis.png",
    },
  ];

  return (
    <main className="fixed inset-0 flex items-center justify-center z-50">
      <div
        className="bg-black opacity-80 absolute inset-0"
        onClick={() => setOneBuyModal(false)}
      ></div>
      <div className="bg-[#f9f9f7] rounded-3xl w-full max-w-xl h-screen max-h-[78vh] my-auto mx-auto relative overflow-y-auto">
        {/* Header with close button */}
        <div className="sticky top-0 bg-[#f9f9f7] p-5 border-b border-gray-200 z-10">
          <div className="relative flex items-center justify-center">
            <h2 className="font-bold text-xl text-black Sora">OnBuy</h2>
            <button
              className="absolute right-0 top-0"
              onClick={() => setFiltersModal(false)}
            >
              <Image
                src={closeIcon}
                height={24}
                width={24}
                alt="close-icon"
                className="cursor-pointer"
              />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-5 space-y-6 Sora">
          {/* Payment Methods */}
          <div className="bg-white rounded-lg p-4 space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-black font-medium">Payment methods</h3>
              <div className="flex space-x-2">
                {paymentMethods.map((method, index) => (
                  <div key={index} className="p-2 object-contain bg-[#F8F7F1]">
                    <Image
                      src={method.src}
                      alt={method.name}
                      width={40}
                      height={40}
                      className="object-contain my-auto"
                    />
                  </div>
                ))}
              </div>
            </div>
            <hr />
            <div className="flex justify-between items-center">
              <h3 className="text-black font-medium">Shipping</h3>
              <span className="text-sm text-gray-700">
                Free shipping 3-5 days
              </span>
            </div>
            <hr />
            <div className="flex justify-between items-center">
              <h3 className="text-black font-medium">Delivery Options</h3>
              <div className="flex space-x-2">
                {deliveryServices.map((service, index) => (
                  <div key={index} className="p-2 object-contain">
                    <Image
                      src={service.src}
                      alt={service.name}
                      width={40}
                      height={40}
                      className="object-contain my-auto"
                    />
                  </div>
                ))}
              </div>
            </div>
            <hr />
            <button className="text-sm underline text-gray-700 hover:underline">
              More information about OnBuy
            </button>
          </div>

          {/* Address */}
          <div className="bg-white rounded-lg p-4 space-y-2">
            <div className="flex justify-between items-start">
              <h3 className="text-black font-medium">Address</h3>
              <div className="text-right text-sm text-gray-700">
                <p>Smartport GmbH (smartport.de)</p>
                <p>Feldstrabe 112 630 Ofenbach am</p>
              </div>
            </div>
            <hr />
            <button className="text-sm text-gray-700 hover:underline">
              All details about smartport
            </button>
          </div>

          {/* Reviews */}
          <div className="bg-[#15361B] Sora rounded-lg p-4 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">Reviews</h3>
                <div className="flex items-center ">
                  <div className="flex">
                    <span className="text-yellow-400 flex mr-1">
                      <FaStar className="w-4 h-4" />
                      <FaStar className="w-4 h-4" />
                      <FaStar className="w-4 h-4" />
                      <FaStar className="w-4 h-4" />
                      <FaStar className="w-4 h-4 text-yellow-400/50" />
                    </span>
                  </div>
                  <span className="font-bold mr-2">4.8</span>
                  <span className="text-sm">(2,101)</span>
                </div>
              </div>
              <div>
                <button className="bg-[#F0381A] text-white rounded-full px-4 py-3 Sora text-sm font-medium w-full">
                  Leave a review
                </button>
              </div>
            </div>

            {/* Review distribution */}
            <div className="space-y-2 mt-5 mb-4 w-1/2">
              <div className="flex items-center">
                <span className="w-12 text-sm">5 stars</span>
                <div className="flex-1 mx-2 bg-white rounded-full h-2 overflow-hidden">
                  <div className="bg-white h-full rounded-full w-0"></div>
                </div>
                <span className="w-8 text-right text-sm">0</span>
              </div>
              <div className="flex items-center">
                <span className="w-12 text-sm">4 stars</span>
                <div className="flex-1 mx-2 bg-white rounded-full h-2 overflow-hidden">
                  <div className="bg-yellow-400 h-full rounded-full w-[80%]"></div>
                </div>
                <span className="w-8 text-right text-sm">700</span>
              </div>
              <div className="flex items-center">
                <span className="w-12 text-sm">3 stars</span>
                <div className="flex-1 mx-2 bg-white rounded-full h-2 overflow-hidden">
                  <div className="bg-yellow-400 h-full rounded-full w-[5%]"></div>
                </div>
                <span className="w-8 text-right text-sm">1</span>
              </div>
              <div className="flex items-center">
                <span className="w-12 text-sm">2 stars</span>
                <div className="flex-1 mx-2 bg-white rounded-full h-2 overflow-hidden">
                  <div className="bg-white h-full rounded-full w-0"></div>
                </div>
                <span className="w-8 text-right text-sm">0</span>
              </div>
              <div className="flex items-center">
                <span className="w-12 text-sm">1 star</span>
                <div className="flex-1 mx-2 bg-white rounded-full h-2 overflow-hidden">
                  <div className="bg-white h-full rounded-full w-0"></div>
                </div>
                <span className="w-8 text-right text-sm">0</span>
              </div>
            </div>

            {/* Leave a review button */}
          </div>
        </div>
      </div>
    </main>
  );
};

export default OneBuyModal;
