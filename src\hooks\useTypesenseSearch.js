"use client";

import { useState, useEffect, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { searchProducts } from "@/server/typesense/search";
import { useRouter } from "next/navigation";

export default function useTypesenseSearch() {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [showResults, setShowResults] = useState(false);
  const router = useRouter();

  //#region  Debouncing search term to avoid too many requests
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);
  //#endregion

  const {
    data: searchResults,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["comparison-vector", debouncedSearchTerm],
    queryFn: async () => {
      console.log("Searching for:", debouncedSearchTerm);
      // Include limit and request category fields
      const results = await searchProducts(debouncedSearchTerm, {
        limit: 5,
        searchType: "vector",
        includeFields:
          "id,name,description,price,priceOld,featuredImage,keywords,standardizedTitle,category_id,category_name,category_slug",
      });
      console.log("Search results from server:", results);
      return results;
    },
    enabled: debouncedSearchTerm.length > 1,
    staleTime: 1000 * 60,
    retry: (failureCount) => {
      if (
        searchResults?.error === "CONNECTION_ERROR" ||
        searchResults?.error === "COLLECTION_NOT_FOUND"
      ) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Handle search input change
  const handleSearchChange = useCallback((e) => {
    const value = e.target.value;
    setSearchTerm(value);
    if (value.length > 0) {
      setShowResults(true);
    } else {
      setShowResults(false);
    }
  }, []);

  // Handle search submission
  const handleSearchSubmit = useCallback(
    (e) => {
      e.preventDefault();
      if (searchTerm.trim()) {
        setShowResults(false);

        // Store the search term before clearing it
        const query = searchTerm.trim();
        console.log("Form submit - navigating to search with query:", query);

        // Store the search term in sessionStorage before navigation
        if (typeof window !== "undefined") {
          sessionStorage.setItem("lastSearchQuery", query);
        }

        // Use window.location.href for a full page navigation
        // This is more reliable than router.push for search page
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
      }
    },
    [searchTerm, router]
  );

  // Handle clicking on a search result
  const handleResultClick = useCallback(
    (productName, category) => {
      setShowResults(false);

      if (category && productName) {
        // Navigate to product page
        router.push(`/${category}/${encodeURIComponent(productName)}`);
        setSearchTerm(""); // Clear search term after navigation
      } else if (productName) {
        // Navigate to search page with the query
        const searchQuery = productName.trim();
        console.log("Navigating to search page with query:", searchQuery);

        // Store the search term in sessionStorage before navigation
        // This ensures the search page can retrieve it even after a navigation
        if (typeof window !== "undefined") {
          sessionStorage.setItem("lastSearchQuery", searchQuery);
        }

        // Use window.location.href for a full page navigation
        // This is more reliable than router.push for search page
        window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
      }
    },
    [router]
  );

  // Close search results when clicking outside
  const handleClickOutside = useCallback(() => {
    setShowResults(false);
  }, []);

  // Log the processed search results that will be passed to components
  const processedResults = searchResults?.success ? searchResults.data : null;
  console.log("Processed search results for UI:", processedResults);

  return {
    searchTerm,
    setSearchTerm,
    searchResults: processedResults,
    isLoading,
    isError,
    error,
    showResults,
    setShowResults,
    handleSearchChange,
    handleSearchSubmit,
    handleResultClick,
    handleClickOutside,
  };
}
