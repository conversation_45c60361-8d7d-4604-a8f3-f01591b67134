"use server";

import { getUserFavoriteProducts } from "@/server/favorites";
import { isAuthenticated } from "@/lib/auth";
import FavoritesPage from "./favorites";

export default async function FavoritesFetch() {
  const user = await isAuthenticated();

  if (!user) {
    // Handle unauthenticated users
    return <FavoritesPage isAuthenticated={false} />;
  }

  // Pre-fetch the data for initial render, but the client component will use React Query
  const response = await getUserFavoriteProducts();
  const favoriteProducts = response.success ? response.data.products : [];

  return (
    <FavoritesPage isAuthenticated={true} initialProducts={favoriteProducts} />
  );
}
