#!/usr/bin/env node

require("dotenv").config();
const Typesense = require("typesense");
const { Pool } = require("pg");

// Create Typesense client
const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: "localhost",
      port: "8109",
      protocol: "http",
    },
  ],
  apiKey: "xyz",
  connectionTimeoutSeconds: 2,
});

// Connect to PostgreSQL
const pool = new Pool({
  connectionString:
    process.env.DATABASE_URL ||
    "**********************************************************",
});

// Define the schema for the products collection with vector support
const productsCollectionSchema = {
  name: "products",
  fields: [
    { name: "id", type: "int32" },
    { name: "name", type: "string" },
    { name: "description", type: "string", optional: true },
    { name: "price", type: "float" },
    { name: "priceOld", type: "float", optional: true },
    { name: "featuredImage", type: "string", optional: true },
    { name: "keywords", type: "string[]", optional: true },
    { name: "standardizedTitle", type: "string", optional: true },
    { name: "category_id", type: "string", optional: true },
    { name: "category_name", type: "string", optional: true },
    { name: "category_slug", type: "string", optional: true },
    { name: "embedding", type: "float[]", optional: true, num_dim: 384 }, // Vector field for embeddings
  ],
  default_sorting_field: "price",
};

// Simple embedding generation function (placeholder)
// In production, you'd want to use the same embedding service as the main app
async function generateSimpleEmbedding(text) {
  // This is a placeholder - in a real implementation, you'd use the same
  // embedding model as in your main application
  // For now, we'll create a dummy embedding
  const embedding = new Array(384).fill(0).map(() => Math.random() * 2 - 1);
  return embedding;
}

async function syncProductsToTypesense() {
  try {
    console.log("Checking if Typesense server is reachable...");

    // Check if Typesense server is reachable
    try {
      const health = await typesenseClient.health.retrieve();
      console.log("Typesense server is reachable:", health);
    } catch (error) {
      console.error("Cannot reach Typesense server:", error.message);
      console.log("Make sure Typesense is running with: docker-compose up -d");
      process.exit(1);
    }

    console.log("Checking if products collection exists...");

    // Check if collection exists, if not create it
    try {
      await typesenseClient.collections("products").retrieve();
      console.log("Products collection already exists");
      
      // Delete and recreate collection to update schema with vector field
      console.log("Deleting existing collection to update schema...");
      await typesenseClient.collections("products").delete();
      console.log("Creating new collection with vector support...");
      await typesenseClient.collections().create(productsCollectionSchema);
      console.log("Collection recreated with vector support");
    } catch (error) {
      if (error.httpStatus === 404) {
        console.log("Collection does not exist, creating new one...");
        await typesenseClient.collections().create(productsCollectionSchema);
        console.log("Created new collection: products");
      } else {
        throw error;
      }
    }

    console.log("Fetching products from PostgreSQL...");

    // Fetch products with categories
    let productsWithCategories = [];

    try {
      // First, try with a limit to test the query
      const testQuery = `
        SELECT
          p.id,
          p.name,
          p.description,
          p.price,
          p."priceOld",
          p."featuredImage",
          p.keywords,
          p."standardizedTitle",
          c.id as category_id,
          c.name as category_name,
          c.slug as category_slug
        FROM product p
        INNER JOIN product_categories_category pcc ON p.id = pcc."productId"
        INNER JOIN category c ON pcc."categoryId" = c.id
        LIMIT 10
      `;
      const { rows: testProducts } = await pool.query(testQuery);

      // If we got results with the limit, remove the limit and get all products
      if (testProducts.length > 0) {
        console.log("Test query successful, now fetching all products...");
        const fullQuery = `
          SELECT
            p.id,
            p.name,
            p.description,
            p.price,
            p."priceOld",
            p."featuredImage",
            p.keywords,
            p."standardizedTitle",
            c.id as category_id,
            c.name as category_name,
            c.slug as category_slug
          FROM product p
          INNER JOIN product_categories_category pcc ON p.id = pcc."productId"
          INNER JOIN category c ON pcc."categoryId" = c.id
          LIMIT 100
        `;
        const { rows: allProducts } = await pool.query(fullQuery);
        productsWithCategories = allProducts;
      } else {
        productsWithCategories = testProducts;
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      throw error;
    }

    console.log(`Found ${productsWithCategories.length} products to sync`);

    if (productsWithCategories.length === 0) {
      console.log("No products found to sync");
      return;
    }

    // Format documents for Typesense with embeddings
    console.log("Generating embeddings and formatting documents...");
    const documents = [];
    
    for (let i = 0; i < productsWithCategories.length; i++) {
      const product = productsWithCategories[i];
      
      // Generate text for embedding
      const textParts = [];
      if (product.name) textParts.push(product.name);
      if (product.description) textParts.push(product.description);
      if (product.standardizedTitle) textParts.push(product.standardizedTitle);
      if (product.keywords && Array.isArray(product.keywords)) {
        textParts.push(product.keywords.join(' '));
      }
      if (product.category_name) textParts.push(product.category_name);
      
      const combinedText = textParts.join(' ').trim();
      
      // Generate embedding (using placeholder function)
      let embedding = [];
      if (combinedText) {
        try {
          embedding = await generateSimpleEmbedding(combinedText);
        } catch (error) {
          console.error(`Failed to generate embedding for product ${product.id}:`, error);
        }
      }
      
      documents.push({
        id: product.id.toString(),
        name: product.name || "",
        description: product.description || "",
        price: product.price || 0,
        priceOld: product.priceOld || 0,
        featuredImage: product.featuredImage || "",
        keywords: product.keywords || [],
        standardizedTitle: product.standardizedTitle || "",
        category_id: product.category_id ? product.category_id.toString() : "",
        category_name: product.category_name || "",
        category_slug: product.category_slug || "",
        embedding: embedding,
      });
      
      if ((i + 1) % 10 === 0) {
        console.log(`Processed ${i + 1}/${productsWithCategories.length} products`);
      }
    }

    console.log(`Importing ${documents.length} documents to Typesense...`);

    // Import documents to Typesense
    try {
      const importResponse = await typesenseClient
        .collections("products")
        .documents()
        .import(documents, { action: "upsert" });

      // Count successes and errors
      const errors = importResponse.filter((res) => res.success === false);
      const successes = importResponse.filter((res) => res.success === true);

      console.log(`Import completed: ${successes.length} successes, ${errors.length} errors`);

      if (errors.length > 0) {
        console.error("Import errors:", errors.slice(0, 5)); // Show first 5 errors
      }

      console.log("✅ Vector-enabled sync completed successfully!");
    } catch (error) {
      console.error("Error importing documents:", error);
      throw error;
    }
  } catch (error) {
    console.error("Sync failed:", error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the sync
syncProductsToTypesense();
