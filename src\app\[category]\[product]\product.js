"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import PriceHistoryChart from "../../components/PriceHistoryChart";
import ProductHighlightsCard from "../../components/ProductHighlightsCard";
import ProductInfo from "../../components/ProductInfo";
import KeyFeatures from "../../components/KeyFeatures";
import BuyOptions from "@/app/components/BuyOptions";
import RelatedProductsContainer from "@/app/components/relatedProducts/index";
import FAQSection from "@/app/components/Faqs";
import ImageZoom from "@/app/components/ImageZoom";
import SchemaOrg from "@/app/components/SchemaOrg";
import cta from "../../assets/images/cta.png";
import home from "../../assets/images/homepage.png";
import arrowright from "../../assets/images/arrowright.png";
import { useFavorites } from "@/app/hooks/useFavorites";
import { FaSpinner } from "react-icons/fa";
import { ToastContainer, Zoom } from "react-toastify";

export default function ProductPage({
  productDetails,
  categoryProduct,
  category,
}) {
  const [selectedImage, setSelectedImage] = useState(0);
  const [pageLoading, setPageLoading] = useState(true);
  const { loading: favoritesLoading } = useFavorites();
  const rawCategory = category || "";
  const categoryName = rawCategory.replace(/-/g, " ");

  useEffect(() => {
    if (productDetails && !favoritesLoading) {
      const timer = setTimeout(() => {
        setPageLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [productDetails, favoritesLoading]);

  const nextSlide = () => {
    setSelectedImage(
      (prev) => (prev + 1) % (productDetails?.gallery?.length || 1)
    );
  };

  const prevSlide = () => {
    setSelectedImage(
      (prev) =>
        (prev - 1 + (productDetails?.gallery?.length || 1)) %
        (productDetails?.gallery?.length || 1)
    );
  };

  // Prepare schema.org data for the product
  const productSchema = productDetails
    ? {
        name: productDetails.name,
        image:
          productDetails.gallery?.length > 0
            ? productDetails.gallery
            : productDetails.featuredImage,
        description: productDetails.description,
        price: productDetails.price,
        priceCurrency: "PKR",
        availability: "https://schema.org/InStock",
        // We'll use relative URLs since window is not available during SSR
        url: `/${category}/${encodeURIComponent(productDetails.name)}`,
      }
    : null;

  // Prepare breadcrumb data
  const breadcrumbSchema = {
    itemListElement: [
      {
        position: 1,
        name: "Home",
        item: "/",
      },
      {
        position: 2,
        name: categoryName,
        item: `/${category}`,
      },
      {
        position: 3,
        name: productDetails?.name || "",
        item: productDetails
          ? `/${category}/${encodeURIComponent(productDetails.name)}`
          : "",
      },
    ],
  };

  return (
    <>
      <ToastContainer
        position="bottom-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      {productDetails && <SchemaOrg type="Product" data={productSchema} />}
      <SchemaOrg type="BreadcrumbList" data={breadcrumbSchema} />

      <div className="min-h-screen mt-22 bg-white">
        <main
          className="pt-20 pb-16"
          itemScope
          itemType="https://schema.org/Product"
        >
          {/* Hidden meta tags for search engines */}
          {productDetails && (
            <>
              <meta itemProp="name" content={productDetails.name} />
              <meta
                itemProp="description"
                content={productDetails.description}
              />
              {productDetails.gallery?.length > 0 &&
                productDetails.gallery.map((img, idx) => (
                  <meta key={`img-${idx}`} itemProp="image" content={img} />
                ))}
              {!productDetails.gallery?.length &&
                productDetails.featuredImage && (
                  <meta
                    itemProp="image"
                    content={productDetails.featuredImage}
                  />
                )}
            </>
          )}

          {pageLoading && (
            <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
              <div className="text-center">
                <FaSpinner className="animate-spin text-4xl text-white mx-auto mb-4" />
                <p className="text-white font-medium">
                  Loading product details...
                </p>
              </div>
            </div>
          )}
          <div className="bg-[#E0F069] py-3">
            <div
              className="mx-auto container flex gap-6 px-2 flex-row"
              itemScope
              itemType="https://schema.org/BreadcrumbList"
            >
              <div
                itemProp="itemListElement"
                itemScope
                itemType="https://schema.org/ListItem"
                className="flex items-center"
              >
                <Link href="/" itemProp="item">
                  <Image src={home} className="w-6 h-6 my-auto" alt="Home" />
                  <meta itemProp="name" content="Home" />
                </Link>
                <meta itemProp="position" content="1" />
              </div>

              <Image
                src={arrowright}
                className="w-3 my-auto h-4"
                alt="Arrow right"
              />

              <div
                itemProp="itemListElement"
                itemScope
                itemType="https://schema.org/ListItem"
              >
                <Link
                  href={`/${category}`}
                  itemProp="item"
                  className="border border-gray-400 Sora text-black p-2 rounded-2xl"
                >
                  <span itemProp="name">{categoryName}</span>
                </Link>
                <meta itemProp="position" content="2" />
              </div>

              <div
                itemProp="itemListElement"
                itemScope
                itemType="https://schema.org/ListItem"
              >
                <span
                  itemProp="name"
                  className="border border-gray-400 Sora text-black p-2 rounded-2xl"
                >
                  {productDetails?.name}
                </span>
                <meta
                  itemProp="item"
                  content={`/${category}/${encodeURIComponent(
                    productDetails?.name || ""
                  )}`}
                />
                <meta itemProp="position" content="3" />
              </div>
            </div>
          </div>
          <div className="container mx-auto">
            <div className="grid grid-cols-12 gap-8 mt-8">
              {/* Left side: Main content */}
              <div className="col-span-8">
                <div className="grid grid-cols-12 gap-4">
                  {/* Main image + thumbnails */}
                  <div className="col-span-5 space-y-4">
                    <div className="relative">
                      {productDetails?.gallery?.length !== 0 && (
                        <button
                          onClick={prevSlide}
                          className="absolute cursor-pointer left-[10px] top-1/2 transform -translate-y-1/2 p-2 bg-white hover:bg-gray-200 rounded-full z-10 border border-gray-300"
                        >
                          <IoIosArrowBack size={24} color="black" />
                        </button>
                      )}

                      <div className="aspect-square w-full rounded-xl overflow-hidden bg-white shadow-sm">
                        <Carousel
                          selectedItem={selectedImage}
                          onChange={setSelectedImage}
                          showThumbs={false}
                          showArrows={false}
                          showStatus={false}
                          showIndicators={false}
                          infiniteLoop
                          swipeable
                        >
                          {productDetails?.gallery?.map((image, index) => (
                            <div
                              key={index}
                              className="relative aspect-square "
                            >
                              <ImageZoom
                                src={image}
                                alt={`Product image ${index + 1}`}
                                className="bg-[#0000001A]"
                                zoomLevel={300}
                              />
                            </div>
                          ))}
                        </Carousel>
                        {productDetails?.gallery?.length === 0 && (
                          <div className="relative aspect-square">
                            <ImageZoom
                              src={productDetails?.featuredImage}
                              alt="featured image"
                              className="bg-[#0000001A]"
                              zoomLevel={300}
                            />
                          </div>
                        )}
                      </div>
                      {productDetails?.gallery?.length !== 0 && (
                        <button
                          onClick={nextSlide}
                          className="absolute cursor-pointer right-[10px] top-1/2 transform -translate-y-1/2 p-2 bg-white hover:bg-gray-200 rounded-full z-10 border border-gray-300"
                        >
                          <IoIosArrowForward size={24} color="black" />
                        </button>
                      )}
                    </div>
                    <div className="grid grid-cols-3 gap-3 w-full">
                      {productDetails?.gallery
                        ?.slice(0, 2)
                        .map((img, index) => (
                          <div
                            key={index}
                            onClick={() => setSelectedImage(index)}
                            className={`w-full aspect-square rounded-md overflow-hidden cursor-pointer`}
                          >
                            <img
                              src={img}
                              alt={`Thumbnail ${index + 1}`}
                              width={300}
                              height={300}
                              className="object-cover w-full h-full rounded-3xl"
                            />
                          </div>
                        ))}
                      {productDetails?.gallery?.length > 3 && (
                        <div className="w-full aspect-square rounded-3xl flex items-center justify-center bg-[#E0F069] text-black text-sm font-semibold cursor-pointer border border-gray-300">
                          <span className="text-center leading-tight text-xl">
                            {productDetails?.gallery?.length - 2} More
                            <br />
                            Images
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Product info */}
                  <div className="col-span-7 space-y-6">
                    <ProductInfo productDetails={productDetails} />
                    <KeyFeatures productDetails={productDetails} />
                  </div>
                </div>
                {/* Tabs for Buy options */}
                <BuyOptions />
              </div>

              {/* Right side: Sidebar */}
              <div className="col-span-4 space-y-4 rounded-lg">
                <PriceHistoryChart />
                <ProductHighlightsCard productDetails={productDetails} />
              </div>
            </div>
            <div className="mt-5">
              <FAQSection productDetails={productDetails} />
            </div>
            <RelatedProductsContainer
              categoryProduct={categoryProduct}
              category={category}
            />
            <div className="container mx-auto px-4 mt-10">
              <Image src={cta} alt="cta" />
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
