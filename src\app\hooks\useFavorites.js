"use client";

import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getUserFavorites } from "@/server/favorites";

// Define a key for the favorites query
export const FAVORITES_QUERY_KEY = ["favorites"];

export function useFavorites() {
  const queryClient = useQueryClient();

  // Use React Query to fetch and cache favorites
  const { data, isLoading, error } = useQuery({
    queryKey: FAVORITES_QUERY_KEY,
    queryFn: async () => {
      const response = await getUserFavorites();
      if (response.success && response.data?.favorites) {
        return new Set(response.data.favorites);
      }
      // Return empty set for unauthorized users or errors
      return new Set();
    },
    // Don't refetch on window focus if we already have data
    refetchOnWindowFocus: (query) => !query.state.data,
    // Keep previous data while fetching
    keepPreviousData: true,
  });

  // Create a custom setter that updates both local state and invalidates the query
  const setFavorites = (updaterFn) => {
    // Get the current favorites
    const currentFavorites =
      queryClient.getQueryData(FAVORITES_QUERY_KEY) || new Set();

    // Apply the updater function to get new favorites
    const newFavorites =
      typeof updaterFn === "function" ? updaterFn(currentFavorites) : updaterFn;

    // Update the query cache immediately
    queryClient.setQueryData(FAVORITES_QUERY_KEY, newFavorites);
  };

  return {
    favorites: data || new Set(),
    setFavorites,
    loading: isLoading,
    error,
  };
}
