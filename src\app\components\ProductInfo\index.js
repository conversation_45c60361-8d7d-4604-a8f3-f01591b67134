"use client";

import { useFavorites } from "@/app/hooks/useFavorites";
import { toggleProductFavorite } from "@/app/utils/favoriteActions";
import { formatCurrency } from "@/app/utils/formatCurrency";
import { FaHeart, FaRegHeart } from "react-icons/fa";

const ProductInfo = ({ productDetails }) => {
  const { favorites, setFavorites } = useFavorites();

  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-start">
        <div>
          <h1
            className="text-2xl font-bold text-[#000000] transformaSansBold"
            itemProp="name"
          >
            {productDetails?.name}
          </h1>
          <div
            className="flex items-center gap-2 mt-2"
            itemProp="offers"
            itemScope
            itemType="https://schema.org/Offer"
          >
            <meta itemProp="priceCurrency" content="PKR" />
            <meta
              itemProp="availability"
              content="https://schema.org/InStock"
            />
            <span className="line-through text-gray-400 text-lg transformaSansNormal">
              {`Rs ${formatCurrency(productDetails?.priceOld)}`}
            </span>
            <span
              className="text-[#F0381A] text-2xl font-semibold transformaSansSemibold"
              itemProp="price"
            >
              {`Rs ${formatCurrency(productDetails?.price)}`}
            </span>
          </div>
          <p
            className="text-gray-500 mt-3 line-clamp-8 transformaSansNormal text-base font-medium leading-relaxed max-w-lg"
            itemProp="description"
          >
            {productDetails?.description}
            <span className="text-[#F0381A] font-bold cursor-pointer ml-1 courser-pointer underline">
              Read More
            </span>
          </p>
        </div>

        <button
          className="bg-gray-100 p-2 rounded-full"
          onClick={(e) => {
            e.stopPropagation();
            toggleProductFavorite(productDetails.id, setFavorites);
          }}
        >
          {favorites.has(productDetails.id) ? (
            <FaHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
          ) : (
            <FaRegHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
          )}
        </button>
      </div>
    </div>
  );
};

export default ProductInfo;
