"use client";
import React from "react";
import Image from "next/image";
import { FaCheckCircle } from "react-icons/fa";

// Mock notification data
const mockNotifications = [
  {
    id: 1,
    title: "Be Sure To Come To Today's Class!",
    message: "We have a special class today at 2:00 PM. Don't miss it!",
    time: "2 hours ago",
    read: false,
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  {
    id: 2,
    title: "Complete Your Profile",
    message:
      "Add more information to your profile to get personalized recommendations.",
    time: "1 day ago",
    read: true,
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
  },
  {
    id: 3,
    title: "Be Sure To Come To Today's Class!",
    message: "We have a special class today at 2:00 PM. Don't miss it!",
    time: "2 days ago",
    read: false,
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  {
    id: 4,
    title: "Be Sure To Come To Today's Class!",
    message: "We have a special class today at 2:00 PM. Don't miss it!",
    time: "3 days ago",
    read: false,
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
];

const NotificationItem = ({ notification }) => {
  return (
    <div className="flex items-start p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer">
      <div className="flex-shrink-0 mr-3">
        <Image
          src={notification.avatar}
          alt="User avatar"
          width={40}
          height={40}
          className="rounded-full"
        />
      </div>
      <div className="flex-grow">
        <div className="flex justify-between items-start">
          <h4 className="text-sm font-semibold text-gray-800">
            {notification.title}
          </h4>
          {notification.read ? null : (
            <span className="w-2 h-2 bg-orange-500 rounded-full mt-2"></span>
          )}
        </div>
        <p className="text-xs text-gray-600 mt-1">{notification.message}</p>
        <div className="flex justify-between items-center mt-2">
          <span className="text-xs text-gray-500">{notification.time}</span>
          <button className="text-xs text-gray-500 hover:underline">
            {notification.read ? "Read" : "Mark as read"}
          </button>
        </div>
      </div>
    </div>
  );
};

const NotificationDropdown = ({ isOpen, setIsOpen }) => {
  const handleMarkAllAsRead = () => {
    // In a real implementation, this would update the notification state
    console.log("Mark all as read");
  };

  const handleViewAllNotifications = () => {
    // In a real implementation, this would navigate to the notifications page
    console.log("View all notifications");
  };

  return (
    <div className="bg-white rounded-lg shadow-lg w-[380px] max-h-[500px] overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 transformaSansSemibold">
          Price Alerts
        </h3>
      </div>

      {/* Notification list */}
      <div className="overflow-y-auto max-h-[350px] Sora">
        {mockNotifications.map((notification) => (
          <NotificationItem key={notification.id} notification={notification} />
        ))}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-100 flex justify-between items-center bg-white">
        <button
          onClick={handleMarkAllAsRead}
          className="text-sm text-gray-600 hover:text-orange-500 flex items-center"
        >
          <FaCheckCircle className="mr-1 Sora" />
          Mark all as read
        </button>
        <button
          onClick={handleViewAllNotifications}
          className="text-sm text-white hover:bg-[#F0381A]/90 bg-[#F0381A] cursor-pointer px-4 py-1 rounded-md"
        >
          View all alerts
        </button>
      </div>
    </div>
  );
};

export default NotificationDropdown;
