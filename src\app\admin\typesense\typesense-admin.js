"use client";

import React, { useState } from "react";
import { syncProductsToTypesense } from "@/server/typesense/sync";
import { FaSpinner } from "react-icons/fa";

export default function TypesenseAdmin() {
  const [syncStatus, setSyncStatus] = useState({
    loading: false,
    success: null,
    message: "",
    error: null,
  });

  const handleSync = async () => {
    try {
      setSyncStatus({
        loading: true,
        success: null,
        message: "Syncing products to Typesense...",
        error: null,
      });

      console.log("Starting Typesense sync...");
      const result = await syncProductsToTypesense();
      console.log("Sync result:", result);

      setSyncStatus({
        loading: false,
        success: result.success,
        message: result.message,
        error: result.error || null,
      });
    } catch (error) {
      console.error("Sync error:", error);
      setSyncStatus({
        loading: false,
        success: false,
        message: "An unexpected error occurred",
        error: error.message,
      });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-2xl border border-gray-200 p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2 text-gray-600">
          Sync Products to Typesense
        </h2>
        <p className="text-gray-600 mb-4">
          This will sync all products from the database to the Typesense search
          engine with category information. This process may take a few minutes
          depending on the number of products.
        </p>

        <button
          onClick={handleSync}
          disabled={syncStatus.loading}
          className={`px-4 py-2 rounded-md ${
            syncStatus.loading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-[#F0381A] hover:bg-[#d32e15] text-white"
          } transition-colors`}
        >
          {syncStatus.loading ? (
            <span className="flex items-center">
              <FaSpinner className="animate-spin mr-2" />
              Syncing...
            </span>
          ) : (
            "Sync Products"
          )}
        </button>
      </div>

      {syncStatus.message && (
        <div
          className={`p-4 rounded-md mt-4 ${
            syncStatus.success === true
              ? "bg-green-50 text-green-700 border border-green-200"
              : syncStatus.success === false
              ? "bg-red-50 text-red-700 border border-red-200"
              : "bg-blue-50 text-blue-700 border border-blue-200"
          }`}
        >
          <p className="font-medium">{syncStatus.message}</p>
          {syncStatus.error && (
            <p className="mt-2 text-sm">Error: {syncStatus.error}</p>
          )}
        </div>
      )}
    </div>
  );
}
