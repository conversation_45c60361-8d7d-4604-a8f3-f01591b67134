import {
  pgTable,
  integer,
  varchar,
  doublePrecision,
  text,
  jsonb,
  timestamp,
} from "drizzle-orm/pg-core";

export const products = pgTable("product", {
  id: integer("id").primaryKey().notNull(),
  description: varchar("description", { length: 255 }),
  featuredImage: varchar("featuredImage", { length: 255 }),
  gallery: text("gallery").array(),
  keyFeatures: text("keyFeatures").array(),
  keywords: text("keywords").array(),
  link: varchar("link", { length: 255 }),
  name: varchar("name", { length: 255 }),
  price: doublePrecision("price"),
  priceOld: doublePrecision("priceOld"),
  productImage: text("productImage").array(),
  prosAndCons: jsonb("prosAndCons"),
  hawkeye_id: varchar("hawkeye_id", { length: 255 }),
  specifications: jsonb("specifications"),
  standardizedTitle: varchar("standardizedTitle", { length: 255 }),
  buyingGuide: jsonb("buyingGuide"),
  faq: jsonb("faq"),
  createdAt: timestamp("createdAt", { withTimezone: false }).defaultNow(),
  updatedAt: timestamp("updatedAt", { withTimezone: false }).defaultNow(),
  deletedAt: timestamp("deletedAt", { withTimezone: false }),
});
