"use client";

import InfoIcon from "../../assets/images/info-icon.png";
import Star from "../../assets/images/star.png";

import Image from "next/image";
const KeyFeatures = ({ productDetails }) => {
  return (
    <div className="pt-5 space-y-6">
      <div className="flex items-center gap-2">
        <Image
          src={InfoIcon}
          alt="InfoIcon"
          className="object-cover"
          height={20}
          width={20}
        />
        <h2 className="text-2xl font-black transformaSansSemibold text-[#000000]">
          Key Features
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 Sora">
        {productDetails?.keyFeatures.map((feature, idx) => (
          <div
            key={idx}
            className="bg-[#F8F7F1] p-2 rounded-xl flex gap-3 items-start"
          >
            <Image
              src={Star}
              alt="Star"
              className="object-cover"
              height={15}
              width={15}
            />
            <p className="text-[#000000CC] text-sm font-normal">{feature}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KeyFeatures;
