"use server";

/**
 * Server-side SchemaOrg component for adding structured data to pages
 * @param {Object} props - Component props
 * @param {string} props.type - Schema.org type (e.g., 'Organization')
 * @param {Object} props.data - Data for the schema
 * @returns {Promise<JSX.Element>} - Schema.org structured data as microdata
 */
export default async function SchemaOrgServer({ type, data }) {
  if (!type || !data) return null;

  switch (type) {
    case "Organization":
      return (
        <div itemScope itemType="https://schema.org/Organization">
          <meta itemProp="name" content={data.name} />
          {data.logo && <meta itemProp="logo" content={data.logo} />}
          {data.url && <meta itemProp="url" content={data.url} />}
          {data.sameAs &&
            data.sameAs.map((url, index) => (
              <meta key={index} itemProp="sameAs" content={url} />
            ))}
        </div>
      );

    case "WebSite":
      return (
        <div itemScope itemType="https://schema.org/WebSite">
          <meta itemProp="name" content={data.name} />
          <meta itemProp="url" content={data.url} />
          {data.potentialAction && (
            <div
              itemProp="potentialAction"
              itemScope
              itemType="https://schema.org/SearchAction"
            >
              <meta itemProp="target" content={data.potentialAction.target} />
              <meta
                itemProp="query-input"
                content={data.potentialAction.queryInput}
              />
            </div>
          )}
        </div>
      );

    default:
      return null;
  }
}
