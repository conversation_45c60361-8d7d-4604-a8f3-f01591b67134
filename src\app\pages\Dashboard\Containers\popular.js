"use client";
import React from "react";
import Image from "next/image";
import Fire from "@/app/assets/images/fire.png";
import PopularProducts from "@/app/components/popular";
import EidBanner from "@/app/components/EidBanner";
// import { useRouter } from "next/navigation";
const PopularContainer = () => {
  // const router = useRouter();

  const handleNavigation = () => {
    // router.push(`/categories`);
  };

  return (
    <div className="bg-[#E0F069] py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-row mb-6 gap-5">
          <h2 className="text-2xl font-bold transformaSansSemibold text-black  text-left">
            Popular Products
          </h2>
          <Image
            src={Fire}
            alt="fire"
            className="object-contain"
            height={20}
            width={20}
          />
        </div>
        <div className="grid grid-cols-12 gap-10">
          <div className="col-span-7">
            <PopularProducts slidePercentage={33.33} />
          </div>
          <div className="col-span-5">
            <EidBanner onClick={() => handleNavigation()} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopularContainer;
