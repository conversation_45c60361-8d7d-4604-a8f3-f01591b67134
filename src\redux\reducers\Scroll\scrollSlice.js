import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isScrolled: false,
};

export const scrollSlice = createSlice({
  name: "scroll",
  initialState,
  reducers: {
    setScrollToTrue: (state) => {
      state.isScrolled = true;
    },
    setScrollToFalse: (state) => {
      state.isScrolled = false;
    },
  },
});

export const { setScrollToTrue, setScrollToFalse } = scrollSlice.actions;
export default scrollSlice.reducer;
