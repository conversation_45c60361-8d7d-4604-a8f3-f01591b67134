"use client";

import React, { useRef, useEffect } from "react";
import { IoSearchOutline } from "react-icons/io5";
import useTypesenseSearch from "@/hooks/useTypesenseSearch";
import SearchResults from "../SearchResults";

const SearchBar = ({ customWidth, customPadding, customBorder }) => {
  const {
    searchTerm,
    searchResults,
    isLoading,
    isError,
    showResults,
    setShowResults,
    handleSearchChange,
    handleSearchSubmit,
    handleResultClick,
  } = useTypesenseSearch();

  console.log("SearchBar received from hook:", {
    searchTerm,
    searchResults,
    isLoading,
    isError,
    showResults,
  });

  const searchRef = useRef(null);

  //#region Handling click outside search dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [setShowResults]);
  //#endregion

  return (
    <div className="relative w-full" ref={searchRef}>
      <form onSubmit={handleSearchSubmit}>
        <div className="relative">
          <input
            type="text"
            className={`block w-full Sora ${
              customWidth ? customWidth : "w-auto"
            }
            ${customPadding ? customPadding : "py-1.5 pr-3 pl-1 "}
            ${
              customBorder
                ? customBorder
                : "border border-gray-300 rounded-full"
            }
            grow text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6`}
            placeholder="Search prices..."
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={() => {
              if (searchTerm.length > 0) {
                setShowResults(true);
              }
            }}
          />
          <button
            type="submit"
            className="absolute inset-y-0 right-0 flex items-center px-4 text-gray-600 cursor-pointer"
          >
            <IoSearchOutline className="bg-[#ff385c] text-white w-8 h-8 p-2 rounded-full" />
          </button>
        </div>
      </form>

      {/* Search Results Dropdown */}
      {showResults && (
        <SearchResults
          searchResults={searchResults}
          isLoading={isLoading}
          isError={isError}
          handleResultClick={handleResultClick}
          searchTerm={searchTerm}
        />
      )}
    </div>
  );
};

export default SearchBar;
