// This script syncs products from the database to Typesense
const { syncProductsToTypesense } = require("../src/server/typesense/sync");

async function main() {
  console.log("Starting Typesense sync...");

  try {
    const result = await syncProductsToTypesense();

    if (result.success) {
      console.log("✅ " + result.message);
      process.exit(0);
    } else {
      console.error("❌ " + result.message);
      if (result.errors) {
        console.error("Errors:", result.errors);
      }
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ Sync failed with error:", error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error("Unhandled error:", error);
  process.exit(1);
});
