"use client";
import React, { useState, useRef } from "react";
import useOutsideClick from "@/app/utils/OutsideClick";

const GlobalDropdown = ({
  children,
  trigger,
  isOpen,
  setIsOpen,
  width = "w-full",
  maxHeight = "h-[15rem]",
  position = "right-0 top-full",
  background = "bg-white",
  border = "",
  rounded = "rounded-lg",
  shadow = "shadow-[0_3px_10px_rgb(0,0,0,0.2)]",
  scrollable = true,
  zIndex = "z-40",
  id,
  className = "",
}) => {
  // If isOpen and setIsOpen are not provided, manage state internally
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const dropdownOpen = isOpen !== undefined ? isOpen : internalIsOpen;
  const setDropdownOpen = setIsOpen || setInternalIsOpen;

  const dropdownRef = useRef(null);

  // Use the existing OutsideClick utility
  useOutsideClick(
    [
      {
        ref: dropdownRef,
        excludeClasses: [".dropdown-trigger"],
        excludeIds: [id || "dropdown-container"],
      },
    ],
    (ref) => {
      if (ref === dropdownRef) setDropdownOpen(false);
    }
  );

  return (
    <div className="relative" id={id || "dropdown-container"}>
      {/* Trigger element */}
      <div
        className="dropdown-trigger"
        onClick={() => setDropdownOpen(!dropdownOpen)}
      >
        {trigger}
      </div>

      {/* Dropdown content */}
      {dropdownOpen && (
        <>
          {/* Optional backdrop for mobile */}
          <div className="fixed inset-0 bg-black opacity-60 z-30 md:hidden"></div>

          {/* Dropdown container */}
          <div
            ref={dropdownRef}
            className={`absolute ${position} ${width} ${maxHeight} ${
              scrollable ? "overflow-auto" : ""
            } ${background} ${border} ${rounded} ${shadow} ${zIndex} ${className}`}
          >
            {children}
          </div>
        </>
      )}
    </div>
  );
};

export default GlobalDropdown;
