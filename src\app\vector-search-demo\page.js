"use client";

import React, { useState } from "react";
import VectorSearchBar from "@/app/components/VectorSearchBar";
import { searchProducts } from "@/server/typesense/search";
import { useQuery } from "@tanstack/react-query";

export default function VectorSearchDemo() {
  const [demoQuery, setDemoQuery] = useState("");
  const [searchType, setSearchType] = useState("hybrid");
  const [showComparison, setShowComparison] = useState(false);

  // Example queries to demonstrate vector search capabilities
  const exampleQueries = [
    {
      query: "fast gaming laptop",
      description: "Semantic search for gaming laptops"
    },
    {
      query: "budget smartphone with good camera",
      description: "Natural language product search"
    },
    {
      query: "wireless headphones for exercise",
      description: "Context-aware search"
    },
    {
      query: "professional video editing workstation",
      description: "Intent-based search"
    }
  ];

  // Comparison search results
  const { data: textResults, isLoading: textLoading } = useQuery({
    queryKey: ["comparison-text", demoQuery],
    queryFn: () => searchProducts(demoQuery, { 
      searchType: "text", 
      limit: 5,
      includeFields: "id,name,description,price,priceOld,featuredImage"
    }),
    enabled: showComparison && demoQuery.length > 2,
  });

  const { data: vectorResults, isLoading: vectorLoading } = useQuery({
    queryKey: ["comparison-vector", demoQuery],
    queryFn: () => searchProducts(demoQuery, { 
      searchType: "vector", 
      limit: 5,
      includeFields: "id,name,description,price,priceOld,featuredImage"
    }),
    enabled: showComparison && demoQuery.length > 2,
  });

  const { data: hybridResults, isLoading: hybridLoading } = useQuery({
    queryKey: ["comparison-hybrid", demoQuery],
    queryFn: () => searchProducts(demoQuery, { 
      searchType: "hybrid", 
      limit: 5,
      includeFields: "id,name,description,price,priceOld,featuredImage"
    }),
    enabled: showComparison && demoQuery.length > 2,
  });

  const handleExampleQuery = (query) => {
    setDemoQuery(query);
    setShowComparison(true);
  };

  const renderSearchResults = (results, isLoading, title, bgColor) => {
    if (isLoading) {
      return (
        <div className={`${bgColor} p-4 rounded-lg`}>
          <h3 className="font-semibold mb-2">{title}</h3>
          <div className="animate-pulse space-y-2">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      );
    }

    if (!results?.success || !results?.data?.hits?.length) {
      return (
        <div className={`${bgColor} p-4 rounded-lg`}>
          <h3 className="font-semibold mb-2">{title}</h3>
          <p className="text-gray-500">No results found</p>
        </div>
      );
    }

    return (
      <div className={`${bgColor} p-4 rounded-lg`}>
        <h3 className="font-semibold mb-2">{title}</h3>
        <div className="space-y-2">
          {results.data.hits.slice(0, 3).map((hit, index) => (
            <div key={hit.document.id} className="bg-white p-3 rounded border">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="font-medium text-sm">{hit.document.name}</p>
                  <p className="text-xs text-gray-600 truncate">
                    {hit.document.description?.substring(0, 80)}...
                  </p>
                </div>
                <div className="text-sm font-semibold text-[#F0381A]">
                  ${hit.document.price?.toFixed(2)}
                </div>
              </div>
              <div className="mt-1 text-xs text-gray-500">
                Rank: #{index + 1}
                {hit.text_match_info && (
                  <span className="ml-2">
                    Score: {hit.text_match_info.score?.toFixed(3)}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Vector Search Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience the power of AI-driven semantic search. Compare traditional text search 
            with vector search and hybrid approaches.
          </p>
        </div>

        {/* Interactive Search Bar */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Try Vector Search</h2>
          <VectorSearchBar />
        </div>

        {/* Example Queries */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Example Queries</h2>
          <p className="text-gray-600 mb-4">
            Try these example queries to see how vector search understands context and intent:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {exampleQueries.map((example, index) => (
              <div 
                key={index}
                className="border border-gray-200 rounded-lg p-4 hover:border-[#F0381A] cursor-pointer transition-colors"
                onClick={() => handleExampleQuery(example.query)}
              >
                <p className="font-medium text-[#F0381A]">"{example.query}"</p>
                <p className="text-sm text-gray-600 mt-1">{example.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Search Comparison */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Search Comparison</h2>
            <div className="flex gap-2">
              <input
                type="text"
                value={demoQuery}
                onChange={(e) => setDemoQuery(e.target.value)}
                placeholder="Enter a search query to compare..."
                className="px-3 py-2 border border-gray-300 rounded-lg"
              />
              <button
                onClick={() => setShowComparison(true)}
                disabled={demoQuery.length < 3}
                className="px-4 py-2 bg-[#F0381A] text-white rounded-lg disabled:bg-gray-300"
              >
                Compare
              </button>
            </div>
          </div>

          {showComparison && demoQuery && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {renderSearchResults(
                textResults, 
                textLoading, 
                "📝 Text Search", 
                "bg-gray-50"
              )}
              {renderSearchResults(
                vectorResults, 
                vectorLoading, 
                "🧠 Vector Search", 
                "bg-purple-50"
              )}
              {renderSearchResults(
                hybridResults, 
                hybridLoading, 
                "🔄 Hybrid Search", 
                "bg-blue-50"
              )}
            </div>
          )}
        </div>

        {/* Information Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">📝 Text Search</h3>
            <p className="text-gray-600 text-sm">
              Traditional keyword-based search. Matches exact words and phrases in product names and descriptions.
            </p>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-purple-600">🧠 Vector Search</h3>
            <p className="text-gray-600 text-sm">
              AI-powered semantic search that understands meaning and context. Finds products based on intent rather than exact keywords.
            </p>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-blue-600">🔄 Hybrid Search</h3>
            <p className="text-gray-600 text-sm">
              Combines the precision of text search with the intelligence of vector search for the best of both worlds.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
