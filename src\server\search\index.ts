"use server";

import { db } from "@/lib/db";
import { categories } from "@/lib/schema";

// Function to get all categories with parent-child relationships
export async function getAllCategoriesForSearch() {
  try {
    // Fetch all categories from the database
    const allCategories = await db
      .select({
        id: categories.id,
        name: categories.name,
        slug: categories.slug,
        parentId: categories.parentId,
      })
      .from(categories);

    // Create a map for easy lookup
    const categoryMap = new Map();
    allCategories.forEach((cat) => {
      categoryMap.set(cat.id, {
        ...cat,
        children: [],
      });
    });

    // Build the tree structure
    const rootCategories = [];
    allCategories.forEach((cat) => {
      if (cat.parentId === null) {
        // This is a root category
        rootCategories.push(categoryMap.get(cat.id));
      } else {
        // This is a child category, add it to its parent
        const parent = categoryMap.get(cat.parentId);
        if (parent) {
          parent.children.push(categoryMap.get(cat.id));
        }
      }
    });

    // Sort categories alphabetically
    rootCategories.sort((a, b) => a.name.localeCompare(b.name));
    rootCategories.forEach((cat) => {
      cat.children.sort((a, b) => a.name.localeCompare(b.name));
    });

    // Convert to the expected format for the sidebar
    const categoryTree = {
      name: "All Categories",
      slug: "search",
      children: rootCategories.map((cat) => ({
        name: cat.name,
        slug: cat.slug,
        children: cat.children.map((child) => ({
          name: child.name,
          slug: child.slug,
          children: [], // We're only going two levels deep for simplicity
        })),
      })),
    };

    return {
      tree: categoryTree,
      selectedSlug: "search",
      selectedPath: ["search"],
    };
  } catch (error) {
    console.error("Error fetching all categories:", error);
    return {
      tree: { name: "All Categories", slug: "search", children: [] },
      selectedSlug: "search",
      selectedPath: ["search"],
    };
  }
}
