"use client";
import { useMemo } from "react";
import React, { useState, useEffect } from "react";
import Sidebar from "../components/sidebar";
import { FaTh, <PERSON>a<PERSON>h<PERSON><PERSON>, <PERSON>aSpinner } from "react-icons/fa";
import Image from "next/image";
import cta from "../assets/images/cta.png";
import home from "../assets/images/homepage.png";
import arrowright from "../assets/images/arrowright.png";
import { useFavorites } from "../hooks/useFavorites";
import { useQuery } from "@tanstack/react-query";
import { searchProducts } from "@/server/typesense/search";
import { renderStars } from "../utils/renderStars";
import { FaRegHeart, FaHeart } from "react-icons/fa";
import { toggleProductFavorite } from "../utils/favoriteActions";
import { formatCurrency } from "../utils/formatCurrency";
import useTypesenseFilters from "@/hooks/useTypesenseFilters";

export default function SearchPage({ initialQuery, allCategories }) {
  const [query, setQuery] = useState("");
  const [selectedSort, setSelectedSort] = useState("price:asc");
  const [layout, setLayout] = useState("grid");
  const [page, setPage] = useState(1);
  const perPage = 20;
  const { favorites, setFavorites, loading: favoritesLoading } = useFavorites();

  //#region Check if theres something on search param, if not take from session storage
  useEffect(() => {
    if (initialQuery) {
      setQuery(initialQuery);
      return;
    }

    // If no initialQuery, check sessionStorage as fallback
    if (typeof window !== "undefined") {
      const storedQuery = sessionStorage.getItem("lastSearchQuery");
      if (storedQuery) {
        console.log("Using query from sessionStorage:", storedQuery);
        setQuery(storedQuery);
        const url = new URL(window.location);
        url.searchParams.set("q", storedQuery);
        window.history.replaceState({}, "", url);
        sessionStorage.removeItem("lastSearchQuery");
      }
    }
  }, [initialQuery]);
  //#endregion

  //#region Resetting page when query changes
  useEffect(() => {
    setPage(1);
  }, [query]);
  //#endregion

  //#region Updating URL when query changes
  useEffect(() => {
    if (typeof window !== "undefined" && query) {
      const url = new URL(window.location);
      if (query) {
        url.searchParams.set("q", query);
      } else {
        url.searchParams.delete("q");
      }
      window.history.replaceState({}, "", url);
    }
  }, [query]);
  //#endregion

  //#region Initializing filter hook
  const {
    selectedFilters,
    categories,
    priceRanges,
    updateFacets,
    toggleFilter,
    clearFilters,
    isFilterSelected,
  } = useTypesenseFilters();
  //#endregion

  //#region Fetching search results
  const {
    data: searchResults,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [
      "searchPage",
      query,
      selectedSort,
      page,
      perPage,
      selectedFilters,
    ],
    queryFn: async () => {
      // Extract price range filters if any
      const priceRangeFilters = selectedFilters.price || [];

      const searchParams = {
        sortBy: selectedSort,
        page,
        perPage,
        includeFields:
          "id,name,description,price,priceOld,featuredImage,keywords,standardizedTitle,category_id,category_name,category_slug",
        priceRangeFilters,
      };
      const results = await searchProducts(query, searchParams);
      if (results?.success && results?.data) {
        updateFacets(results.data);
      }
      return results;
    },
    enabled: query.length > 0,
    staleTime: 0,
    cacheTime: 1000 * 60 * 5,
    refetchOnMount: "always",
    refetchOnWindowFocus: false,
    retry: 1,
  });
  //#endregion

  const handleSortChange = (event) => {
    setSelectedSort(event.target.value);
  };

  const handleLayoutChange = (newLayout) => {
    setLayout(newLayout);
  };

  // We'll calculate totalPages after displayData is initialized

  // Use the allCategories from the server or fallback to a default
  const categoriesForSidebar = allCategories || {
    tree: { name: "All Categories", slug: "search", children: [] },
    selectedSlug: "search",
    selectedPath: ["search"],
  };

  const searchResultCategories = React.useMemo(() => {
    if (searchResults?.data?.hits?.length) {
      const categoryMap = new Map();
      searchResults.data.hits.forEach((hit) => {
        if (hit.document.category_id && hit.document.category_name) {
          categoryMap.set(hit.document.category_id, {
            id: hit.document.category_id,
            name: hit.document.category_name,
            slug: hit.document.category_slug || "",
          });
        }
      });
      return Array.from(categoryMap.values());
    }

    // Fallback to using the paginated search results if sidebar data isn't available
    if (!searchResults?.data?.hits?.length) return [];

    const categoryMap = new Map();
    searchResults.data.hits.forEach((hit) => {
      if (hit.document.category_id && hit.document.category_name) {
        categoryMap.set(hit.document.category_id, {
          id: hit.document.category_id,
          name: hit.document.category_name,
          slug: hit.document.category_slug || "",
        });
      }
    });

    return Array.from(categoryMap.values());
  }, [searchResults?.data?.hits]);

  console.log(searchResults, "searching the results");
  console.log("Categories from search results:", searchResultCategories);

  // Get filtered results based on selected filters
  const filteredData = useMemo(() => {
    if (!searchResults?.success || !searchResults?.data) {
      return { hits: [] };
    }

    // If no filters selected, return original results
    if (Object.keys(selectedFilters).length === 0) {
      return searchResults.data;
    }

    // Check if we only have price filters (which are now handled server-side)
    const hasOnlyPriceFilters =
      Object.keys(selectedFilters).length === 1 &&
      Object.keys(selectedFilters).includes("price");

    if (hasOnlyPriceFilters) {
      // Price filters are already applied by Typesense, return the results as is
      return searchResults.data;
    }

    // Create a simple client-side filter function for category filters
    const filteredHits = searchResults.data.hits.filter((hit) => {
      // Check each filter type
      for (const [filterType, values] of Object.entries(selectedFilters)) {
        if (values.length === 0) continue;

        // Category filter - we still handle this client-side
        if (filterType === "category_name") {
          if (!values.includes(hit.document.category_name)) {
            return false;
          }
        }

        // We don't need to handle price filters here anymore
        // They are now handled by Typesense server-side
      }

      // If we get here, the hit matches all filters
      return true;
    });

    // Return filtered results in the same format as the original data
    return {
      ...searchResults.data,
      hits: filteredHits,
      found: filteredHits.length,
    };
  }, [searchResults, selectedFilters]);

  // Use filtered data for display
  const displayData = filteredData;

  // Calculate total pages based on filtered data
  const totalPages = useMemo(() => {
    if (displayData?.found) {
      return Math.ceil(displayData.found / perPage);
    }
    return 0;
  }, [displayData, perPage]);

  return (
    <div className="min-h-screen py-12 mt-22">
      <div className="bg-[#E0F069] py-3 mt-8">
        <div className="mx-auto container flex gap-6 flex-row">
          <Image src={home} className="w-6 h-6 my-auto" alt="Home" />
          <Image src={arrowright} className="w-3 my-auto h-4" alt="Arrow" />
          <div className="border border-gray-400 Sora text-black p-2 rounded-2xl">
            Search Results
          </div>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-10 container mx-auto px-4 mt-8">
        <div className="col-span-3">
          <Sidebar
            subcategories={categoriesForSidebar}
            categories={categories}
            priceRanges={priceRanges}
            selectedFilters={selectedFilters}
            toggleFilter={toggleFilter}
            clearFilters={clearFilters}
            isFilterSelected={isFilterSelected}
            query={query}
            setQuery={setQuery}
          />
        </div>
        <div className="col-span-9">
          <div className="flex justify-between mb-8">
            <h2 className="text-2xl font-bold text-black transformaSansBold text-left">
              {query ? `Results for "${query}"` : "Search Products"}
            </h2>
            <div className="flex items-center gap-4">
              <label className="text-gray-500 text-sm transformaSansNormal">
                Sort by:
              </label>
              <select
                value={selectedSort}
                onChange={handleSortChange}
                className="border border-gray-300 bg-[#F8F7F1] transformaSansNormal text-black rounded-md py-1 text-sm"
              >
                <option value="price:asc">Price: Low to High</option>
                <option value="price:desc">Price: High to Low</option>
                <option value="_text_match:desc">Relevance</option>
              </select>

              {/* Layout Toggle Buttons */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleLayoutChange("grid")}
                  className={`p-2 rounded ${
                    layout === "grid"
                      ? "bg-red-500 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                >
                  <FaTh size={18} />
                </button>

                <button
                  onClick={() => handleLayoutChange("list")}
                  className={`p-2 rounded ${
                    layout === "list"
                      ? "bg-red-500 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                >
                  <FaThList size={18} />
                </button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
                <div className="text-center">
                  <FaSpinner className="animate-spin text-4xl text-white mx-auto mb-4" />
                  <p className="text-white font-medium">Loading products...</p>
                </div>
              </div>
            </div>
          ) : isError || (searchResults && !searchResults.success) ? (
            <div className="bg-red-50 text-red-600 p-4 rounded-md">
              <p className="font-medium mb-2">
                {searchResults?.message ||
                  "An error occurred while searching. Please try again."}
              </p>
            </div>
          ) : !query ? (
            <></>
          ) : displayData?.hits?.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <p className="text-lg text-gray-600 mb-4">
                No results found for "{query}"
              </p>
              <p className="text-gray-500">
                Try using different keywords or check your spelling
              </p>
            </div>
          ) : (
            <>
              {displayData?.similarModelFound && (
                <div className="mb-4 border-l-4 border-yellow-400 bg-yellow-50 p-4">
                  <p className="text-lg font-medium text-yellow-800">
                    We couldn't find exact matches for "{query}"
                  </p>
                  <p className="text-yellow-700 mt-1">
                    However, we found similar products that might interest you
                  </p>
                </div>
              )}

              <div
                className={`grid grid-cols-1 md:grid-cols-2 ${
                  layout === "list" ? "lg:grid-cols-1" : "lg:grid-cols-4"
                } gap-4`}
              >
                {(displayData?.hits || []).map((hit) => (
                  <div
                    key={hit.document.id}
                    className="bg-white p-4 border cursor-pointer border-[#EBEBEB] rounded-lg relative hover:shadow-lg hover:scale-[1.02] transition-all duration-200 group"
                    onClick={() => {
                      // Navigate to category-based product route if category_slug is available
                      if (hit.document.category_slug && hit.document.name) {
                        // Use product name instead of ID for the URL
                        const productSlug = encodeURIComponent(
                          hit.document.name
                        );
                        window.location.href = `/${hit.document.category_slug}/${productSlug}`;
                      } else {
                        // Fallback to product route if no category is available
                        const productSlug = encodeURIComponent(
                          hit.document.name || hit.document.id
                        );
                        window.location.href = `/product/${productSlug}`;
                      }
                    }}
                  >
                    <span className="absolute top-2 left-2 bg-[#F0381A] text-white px-2 py-1 text-xs rounded-lg Sora">
                      Bestseller
                    </span>
                    {favoritesLoading ? (
                      <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full">
                        <div className="bg-gray-100 p-1.5 rounded-full">
                          <div className="animate-pulse w-5 h-5 rounded-full bg-gray-300"></div>
                        </div>
                      </div>
                    ) : (
                      <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full cursor-pointer hover:bg-opacity-100 transition">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleProductFavorite(
                              hit.document.id,
                              setFavorites
                            );
                          }}
                          className="bg-gray-100 p-1.5 rounded-full hover:bg-gray-200 transition-colors"
                        >
                          {favorites.has(hit.document.id) ? (
                            <FaHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                          ) : (
                            <FaRegHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                          )}
                        </button>
                      </div>
                    )}
                    <img
                      src={hit.document.featuredImage}
                      alt={hit.document.name}
                      className="w-full h-40 mt-2 object-contain rounded-md"
                      width={500}
                      height={500}
                    />
                    <div className="h-18">
                      <h3
                        className="text-sm text-left Sora text-gray-400 mt-2 hover:text-[#F0381A] cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (hit.document.category_slug) {
                            window.location.href = `/${hit.document.category_slug}`;
                          }
                        }}
                      >
                        {hit.document.category_name || "Search Results"}
                      </h3>
                      <h3 className="text-lg font-semibold Sora text-left text-gray-900 line-clamp-2 group-hover:text-[#F0381A] transition-colors">
                        {hit.document.name}
                      </h3>
                    </div>
                    <div className="flex items-center flex-row mb-2 mt-5 ">
                      <div className="bg-[#F0F0F0] flex flex-row px-2 py-1 rounded-full">
                        <div className="flex mr-2 my-auto">
                          {renderStars(4)}
                        </div>
                        <span className="text-sm text-gray-600">{4}</span>
                      </div>
                    </div>
                    <div className="flex flex-row gap-3 mt-2 my-auto">
                      <p className="text-gray-500 my-auto text-base line-through">
                        {hit.document.priceOld > 0
                          ? `Rs ${formatCurrency(hit.document.priceOld)}`
                          : ""}
                      </p>
                      <p className="text-red-500 font-bold text-lg">
                        {`Rs ${formatCurrency(hit.document.price)}`}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mx-auto flex justify-center mt-8">
                  <nav className="flex items-center gap-1 text-black Sora">
                    <button
                      onClick={() => setPage((p) => Math.max(1, p - 1))}
                      disabled={page === 1}
                      className="px-3 py-1 rounded border cursor-pointer border-gray-300 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <span className="px-4 py-1">
                      Page {page} of {totalPages}
                    </span>
                    <button
                      onClick={() =>
                        setPage((p) => Math.min(totalPages, p + 1))
                      }
                      disabled={page === totalPages}
                      className="px-3 py-1 rounded border border-gray-300 cursor-pointer disabled:opacity-50"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              )}
            </>
          )}
        </div>
      </div>
      <div className="container mx-auto px-4 mt-10">
        <Image src={cta} alt="cta" />
      </div>
    </div>
  );
}
