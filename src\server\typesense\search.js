"use server";

import typesenseServerClient from "@/lib/typesense-server";
import { vectorSearchProducts, hybridSearchProducts } from "./vector-search";

export async function searchProducts(query, options = {}) {
  try {
    if (!query || query.trim() === "") {
      return { success: true, data: { hits: [] } };
    }

    const {
      limit = 10,
      page = 1,
      perPage = 10,
      sortBy = "price:asc",
      includeFields,
      facetBy = [],
      facetQuery = [],
      filterBy = "",
      maxFacetValues = 10,
      priceRangeFilters = [],
      searchType = "text", // "text", "vector", or "hybrid"
    } = options;

    // Route to appropriate search function based on searchType
    if (searchType === "vector") {
      return await vectorSearchProducts(query, options);
    }

    if (searchType === "hybrid") {
      return await hybridSearchProducts(query, options);
    }

    // Continue with text search (default behavior)

    const searchParameters = {
      q: query,
      query_by: "name,description,keywords,standardizedTitle",
      sort_by: sortBy,
      page,
      per_page: perPage,
      limit,
      prefix: true,
      cache: true,
      prioritize_exact_match: true,
      prioritize_token_position: true,
      // Add text search specific parameters
      typo_tokens_threshold: 1,
      drop_tokens_threshold: 1,

      // Include category fields in the response
      include_fields:
        includeFields ||
        "id,name,description,price,priceOld,featuredImage,keywords,standardizedTitle,category_id,category_name,category_slug",

      // Highlighting settings
      highlight_full_fields: "name",
      highlight_affix_num_tokens: 2,
      highlight_start_tag: "<mark>",
      highlight_end_tag: "</mark>",

      // Typo tolerance settings
      num_typos: 1,
      drop_tokens_threshold: 0,
      typo_tokens_threshold: 1,
      min_len_1typo: 4,
      min_len_2typo: 8,

      // Important: For facets, we want to consider ALL matching documents
      // not just the paginated results
      facet_query_num_typos: 0,
      facet_results_per_page: 1000, // Get all facet values
    };

    // Add facet_by if provided
    if (facetBy && facetBy.length > 0) {
      searchParameters.facet_by = facetBy.join(",");
    }

    // Add facet query if provided
    if (facetQuery && facetQuery.length > 0) {
      searchParameters.facet_query = facetQuery;
    }

    // Process price range filters
    let filterByValue = filterBy;

    if (priceRangeFilters && priceRangeFilters.length > 0) {
      const priceFilters = priceRangeFilters
        .map((range) => {
          const [minStr, maxStr] = range.split("-");
          const min = parseFloat(minStr);
          const max = maxStr && maxStr.length > 0 ? parseFloat(maxStr) : null;

          if (!isNaN(min)) {
            if (max !== null && !isNaN(max)) {
              return `(price:>=${min} && price:<=${max})`;
            } else {
              return `price:>=${min}`;
            }
          }
          return null;
        })
        .filter(Boolean);

      if (priceFilters.length > 0) {
        const priceFilterStr = priceFilters.join(" || ");
        filterByValue = filterByValue
          ? `${filterByValue} && (${priceFilterStr})`
          : priceFilterStr;
      }
    }

    // Add filter_by if we have any filters
    if (filterByValue) {
      searchParameters.filter_by = filterByValue;
    }

    try {
      const searchResults = await typesenseServerClient
        .collections("products")
        .documents()
        .search(searchParameters);

      // Log search results for debugging
      if (searchResults.found > 0) {
        console.log(
          `Found ${searchResults.found} results for query: "${query}"`
        );
        if (searchResults.hits && searchResults.hits.length > 0) {
          console.log("First hit:", searchResults.hits[0].document.name);
        }
      } else {
        console.log(`No results found for query: "${query}"`);
      }

      // Default return when no similar models were found
      return {
        success: true,
        data: {
          ...searchResults,
          search_metadata: {
            type: "text_search",
            note: "Traditional keyword-based search",
          },
        },
        searchType: "text",
      };
    } catch (searchError) {
      console.error("Typesense search error:", searchError);
      console.error("Error details:", JSON.stringify(searchError, null, 2));

      // Check if it's a connection error
      if (searchError.code === "ECONNREFUSED") {
        return {
          success: false,
          message:
            "Could not connect to Typesense server. Please make sure Typesense is running.",
          error: "CONNECTION_ERROR",
        };
      }

      // Check if it's a collection not found error
      if (
        searchError.httpStatus === 404 &&
        searchError.message.includes("Collection not found")
      ) {
        return {
          success: false,
          message:
            "Products collection not found. Please run the data sync first.",
          error: "COLLECTION_NOT_FOUND",
        };
      }

      // Handle other specific error types
      if (searchError.httpStatus === 400) {
        return {
          success: false,
          message:
            "Invalid search query format. Please try a different search term.",
          error: "INVALID_QUERY",
          details: searchError.message,
        };
      }

      // Generic error handler
      return {
        success: false,
        message: "An error occurred while searching. Please try again.",
        error: "SEARCH_ERROR",
        details: searchError.message,
      };
    }
  } catch (error) {
    console.error("Error searching products:", error);
    return {
      success: false,
      message: "Failed to search products",
      error: error.message,
    };
  }
}
