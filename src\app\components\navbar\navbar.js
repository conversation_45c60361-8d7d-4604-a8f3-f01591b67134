import NavbarClient from "./index";
import CategoriesServer from "../categories/CategoriesComponent";
import { getCategories } from "@/server/categories";
import { cookies } from "next/headers";

export default async function Navbar({}) {
  const categories = await getCategories();
  const cookieStore = await cookies();
  const token = cookieStore.get("auth_token")?.value;
  return (
    <div>
      <NavbarClient categories={categories} token={token} />
      <CategoriesServer fixed="true" />
    </div>
  );
}
