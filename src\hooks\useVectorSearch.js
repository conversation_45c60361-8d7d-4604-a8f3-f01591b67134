"use client";

import { useState, useEffect, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { searchProducts } from "@/server/typesense/search";
import { useRouter } from "next/navigation";

export default function useVectorSearch() {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [searchType, setSearchType] = useState("hybrid"); // "text", "vector", or "hybrid"
  const router = useRouter();

  //#region  Debouncing search term to avoid too many requests
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);
  //#endregion

  const {
    data: searchResults,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["vectorSearch", debouncedSearchTerm, searchType],
    queryFn: async () => {
      console.log(`Performing ${searchType} search for:`, debouncedSearchTerm);
      
      const results = await searchProducts(debouncedSearchTerm, {
        limit: 5,
        searchType: searchType,
        includeFields:
          "id,name,description,price,priceOld,featuredImage,keywords,standardizedTitle,category_id,category_name,category_slug",
      });
      
      console.log(`${searchType} search results:`, results);
      return results;
    },
    enabled: debouncedSearchTerm.length > 1,
    staleTime: 1000 * 60,
    retry: (failureCount) => {
      if (
        searchResults?.error === "CONNECTION_ERROR" ||
        searchResults?.error === "COLLECTION_NOT_FOUND"
      ) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Handle search input change
  const handleSearchChange = useCallback((e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setShowResults(value.length > 1);
  }, []);

  // Handle search type change
  const handleSearchTypeChange = useCallback((newSearchType) => {
    setSearchType(newSearchType);
    console.log("Search type changed to:", newSearchType);
  }, []);

  // Handle search submission
  const handleSearchSubmit = useCallback(
    (e) => {
      e.preventDefault();
      if (searchTerm.trim()) {
        setShowResults(false);

        // Store the search term before clearing it
        const query = searchTerm.trim();
        console.log("Form submit - navigating to search with query:", query);

        // Store the search term and type in sessionStorage before navigation
        if (typeof window !== "undefined") {
          sessionStorage.setItem("lastSearchQuery", query);
          sessionStorage.setItem("lastSearchType", searchType);
        }

        // Use window.location.href for a full page navigation
        // Include search type in the URL
        window.location.href = `/search?q=${encodeURIComponent(query)}&type=${searchType}`;
      }
    },
    [searchTerm, searchType, router]
  );

  // Handle clicking on a search result
  const handleResultClick = useCallback(
    (product) => {
      setShowResults(false);
      setSearchTerm("");
      
      // Navigate to product page
      const categorySlug = product.category_slug || "products";
      const productName = product.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-+|-+$/g, "");
      
      router.push(`/${categorySlug}/${productName}`);
    },
    [router]
  );

  // Handle clicking outside to close results
  const handleClickOutside = useCallback(() => {
    setShowResults(false);
  }, []);

  return {
    searchTerm,
    searchResults,
    isLoading,
    isError,
    error,
    showResults,
    searchType,
    setShowResults,
    handleSearchChange,
    handleSearchSubmit,
    handleResultClick,
    handleClickOutside,
    handleSearchTypeChange,
  };
}
