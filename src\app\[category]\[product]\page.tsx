"use server";

import { getProductByName } from "../../../server/product/index";
import { getProductsByCategory } from "../../../server/products/index";

import ProductPage from "./product";

type PageParams = Promise<{ category: string; product: string }>;

export default async function ProductFetch({ params }: { params: PageParams }) {
  const { product, category } = await params;
  const decodedProduct = decodeURIComponent(product);
  const productDetails = await getProductByName(decodedProduct);
  const categoryProducts = await getProductsByCategory(category);
  return (
    <ProductPage
      productDetails={productDetails}
      categoryProduct={categoryProducts}
      category={category}
    />
  );
}
