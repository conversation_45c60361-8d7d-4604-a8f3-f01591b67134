"use client";
import React, { useState } from "react";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { renderStars } from "../utils/renderStars";
import { useParams, useRouter } from "next/navigation";
import { FaRegHeart, FaHeart } from "react-icons/fa";
import { useFavorites } from "../hooks/useFavorites";
import { toggleProductFavorite } from "../utils/favoriteActions";
import { formatCurrency } from "../utils/formatCurrency";

const CategoryProducts = ({
  slidePercentage = null,
  currentSlideNumber,
  fullView = false,
  layout = null,
  categoriesProducts,
}) => {
  const [currentSlide, setCurrentSlide] = useState(currentSlideNumber || 0);
  const { favorites, setFavorites, loading: favoritesLoading } = useFavorites();
  const params = useParams();
  const router = useRouter();
  const rawCategory = params?.category || "";
  // Function to capitalize each word in a string
  const capitalizeWords = (str) => {
    return str
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };
  const categoryName = capitalizeWords(rawCategory.replace(/-/g, " "));
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % products.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + products.length) % products.length);
  };

  const handleNavigation = (name) => {
    const formattedCategory = encodeURIComponent(
      rawCategory.toLowerCase().replace(/ /g, "-")
    );
    const formattedProductName = encodeURIComponent(
      name.toLowerCase().replace(/ /g, "-")
    );
    router.push(`/${formattedCategory}/${formattedProductName}`);
  };

  return (
    <div>
      {fullView ? (
        <div
          className={`grid grid-cols-1 md:grid-cols-2 ${
            layout === "list" ? "lg:grid-cols-1" : "lg:grid-cols-4"
          } gap-4`}
        >
          {categoriesProducts.map((product) => (
            <div
              key={product.id}
              className="bg-white p-4 border cursor-pointer border-[#EBEBEB] rounded-lg relative hover:shadow-lg hover:scale-[1.02] transition-all duration-200 group"
              onClick={() => handleNavigation(product.name)}
            >
              <span className="absolute top-2 left-2 bg-[#F0381A] text-white px-2 py-1 text-xs rounded-lg Sora">
                Bestseller
              </span>
              {favoritesLoading ? (
                <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full">
                  <div className="bg-gray-100 p-1.5 rounded-full">
                    <div className="animate-pulse w-5 h-5 rounded-full bg-gray-300"></div>
                  </div>
                </div>
              ) : (
                <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full cursor-pointer hover:bg-opacity-100 transition">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleProductFavorite(product.id, setFavorites);
                    }}
                    className="bg-gray-100 p-1.5 rounded-full hover:bg-gray-200 transition-colors"
                  >
                    {favorites.has(product.id) ? (
                      <FaHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                    ) : (
                      <FaRegHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                    )}
                  </button>
                </div>
              )}
              <img
                src={product.featuredImage}
                alt={product.name}
                className="w-full h-40 mt-2 object-contain rounded-md"
                width={500}
                height={500}
              />
              <div className="h-18">
                <h3 className="text-sm text-left Sora text-gray-400 mt-2">
                  {categoryName}
                </h3>
                <h3 className="text-lg font-semibold Sora text-left text-gray-900 line-clamp-2 group-hover:text-[#F0381A] transition-colors">
                  {product.name}
                </h3>
              </div>
              <div className="flex items-center flex-row mb-2 mt-5 ">
                <div className="bg-[#F0F0F0] flex flex-row px-2 py-1 rounded-full">
                  <div className="flex mr-2 my-auto">{renderStars(4)}</div>
                  <span className="text-sm text-gray-600">{4}</span>
                </div>
              </div>
              <div className="flex flex-row gap-3 mt-2 my-auto">
                <p className="text-gray-500 my-auto text-base line-through">
                  {`Rs ${formatCurrency(product.priceOld)}`}
                </p>
                <p className="text-red-500 font-bold text-lg">
                  {`Rs ${formatCurrency(product.price)}`}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="relative flex items-center">
          {/* Left Arrow */}
          <button
            onClick={prevSlide}
            className="absolute left-[-20px] top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full z-10 border border-gray-300"
          >
            <IoIosArrowBack size={24} color="black" />
          </button>

          <div className="w-full">
            <Carousel
              selectedItem={currentSlide}
              onChange={setCurrentSlide}
              showThumbs={false}
              showStatus={false}
              infiniteLoop
              centerMode
              centerSlidePercentage={slidePercentage}
              showArrows={false}
              showIndicators={false}
            >
              {categoriesProducts.map((product) => (
                <div
                  key={product.id}
                  className="bg-white h-90 p-4 border border-[#EBEBEB] rounded-lg relative mr-2 hover:shadow-lg hover:scale-[1.02] transition-all duration-200 group"
                >
                  <span className="absolute top-2 left-2 bg-[#F0381A] text-white px-2 py-1 text-xs rounded-lg Sora">
                    Bestseller
                  </span>
                  {favoritesLoading ? (
                    <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full">
                      <div className="bg-gray-100 p-1.5 rounded-full">
                        <div className="animate-pulse w-5 h-5 rounded-full bg-gray-300"></div>
                      </div>
                    </div>
                  ) : (
                    <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full cursor-pointer hover:bg-opacity-100 transition">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleProductFavorite(product.id, setFavorites);
                        }}
                        className="bg-gray-100 p-1.5 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        {favorites.has(product.id) ? (
                          <FaHeart className="text-red-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                        ) : (
                          <FaRegHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                        )}
                      </button>
                    </div>
                  )}
                  <img
                    src={product.featuredImage}
                    alt={product.name}
                    className="w-full h-40 object-contain rounded-md"
                    width={500}
                    height={500}
                  />
                  <div className="h-17">
                    <h3 className="text-sm text-left text-gray-400 mt-4">
                      {categoryName}
                    </h3>
                    <h3 className="text-xl font-semibold Sora text-left text-gray-900 group-hover:text-[#F0381A] transition-colors">
                      {product.name}
                    </h3>
                  </div>
                  <div className="flex items-center mb-2 mt-5">
                    <div className="flex mr-2">{renderStars(4)}</div>
                    <span className="text-sm text-gray-600">{4}</span>
                  </div>
                  <div className="flex flex-row gap-3 mt-2">
                    <p className="text-gray-500 my-auto text-base line-through">
                      {`Rs ${formatCurrency(product.priceOld)}`}
                    </p>
                    <p className="text-red-500 font-bold text-lg">
                      {`Rs ${formatCurrency(product.price)}`}
                    </p>
                  </div>
                </div>
              ))}
            </Carousel>
          </div>

          {/* Right Arrow */}
          <button
            onClick={nextSlide}
            className="absolute right-[-20px] top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full z-10 border border-gray-300"
          >
            <IoIosArrowForward size={24} color="black" />
          </button>
        </div>
      )}
    </div>
  );
};

export default CategoryProducts;
