"use client";
import React, { useState } from "react";
import Iconsalarmsharp from "../../assets/images/ion_alarm-sharp.png";
import Image from "next/image";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

const PriceHistoryChart = () => {
  const [chartData] = useState({
    series: [
      {
        name: "Price",
        data: [35000, 29000, 31000, 26000, 25000, 24000],
      },
    ],
    options: {
      chart: {
        type: "line",
        toolbar: { show: false },
      },
      colors: ["#FF4B3E"],
      dataLabels: {
        enabled: true,
        style: {
          colors: ["#F0381A"],
        },
      },
      stroke: {
        curve: "straight",
      },
      xaxis: {
        categories: [
          "23 Dec 24",
          "23 Dec 24",
          "23 Dec 24",
          "23 Dec 24",
          "23 Dec 24",
          "23 Dec 24",
        ],
        labels: { style: { colors: "#fff" } },
      },
      yaxis: {
        labels: { style: { colors: "#fff" } },
      },
      grid: {
        borderColor: "#2E4237",
        strokeDashArray: 0,
        xaxis: {
          lines: {
            show: true,
          },
        },
        yaxis: {
          lines: {
            show: true,
          },
        },
      },

      tooltip: {
        theme: "dark",
      },
    },
  });

  return (
    <div className="bg-[#15361B] p-4 rounded-xl w-full">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-white font-semibold text-lg">Price History</h2>
          <p className="text-gray-400 text-sm">
            Track price fluctuations over time
          </p>
        </div>
        <button className="bg-[#F0381A] text-white text-sm font-medium py-2 px-4 rounded-2xl flex items-center gap-2">
          <span>
            <Image
              src={Iconsalarmsharp}
              alt="Iconsalarmsharp"
              className="object-cover"
              height={20}
              width={20}
            />
          </span>{" "}
          Set a Price Alert
        </button>
      </div>
      <Chart
        options={chartData.options}
        series={chartData.series}
        type="line"
        height={250}
      />
    </div>
  );
};

export default PriceHistoryChart;
