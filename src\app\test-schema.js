"use client";

import React from "react";
import Link from "next/link";

export default function TestSchema() {
  return (
    <div className="container mx-auto py-20 px-4 text-black">
      <h1 className="text-3xl font-bold mb-8">
        Schema.org Structured Data Test
      </h1>

      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">
          How to Test Schema.org Implementation
        </h2>
        <ol className="list-decimal pl-6 space-y-2">
          <li>Visit a product page on your site</li>
          <li>
            Use one of these tools to validate your structured data:
            <ul className="list-disc pl-6 mt-2">
              <li>
                <a
                  href="https://validator.schema.org/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Schema.org Validator
                </a>
              </li>
              <li>
                <a
                  href="https://search.google.com/test/rich-results"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Google Rich Results Test
                </a>
              </li>
              <li>
                <a
                  href="https://www.bing.com/webmaster/tools/markup-validator"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Bing Markup Validator
                </a>
              </li>
            </ul>
          </li>
          <li>Enter your product page URL or paste the HTML</li>
          <li>Check if the structured data is correctly recognized</li>
        </ol>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Implemented Schema Types</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li>
            <strong>Product</strong> - On product pages
          </li>
          <li>
            <strong>BreadcrumbList</strong> - On product pages
          </li>
          <li>
            <strong>Organization</strong> - Site-wide in layout
          </li>
          <li>
            <strong>WebSite</strong> - Site-wide in layout
          </li>
        </ul>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Implementation Methods</h2>
        <p className="mb-4">
          We&apos;ve implemented schema.org structured data using two
          approaches:
        </p>

        <div className="mb-4">
          <h3 className="text-lg font-medium mb-2">1. Hidden Metadata</h3>
          <p>Using the SchemaOrg component that adds invisible meta tags</p>
          <pre className="bg-gray-100 p-3 rounded mt-2 overflow-x-auto">
            {`<SchemaOrg type="Product" data={productSchema} />`}
          </pre>
        </div>

        <div>
          <h3 className="text-lg font-medium mb-2">2. Microdata Attributes</h3>
          <p>
            Adding itemScope, itemType, and itemProp attributes directly to HTML
            elements
          </p>
          <pre className="bg-gray-100 p-3 rounded mt-2 overflow-x-auto">
            {`<div itemScope itemType="https://schema.org/Product">
  <h1 itemProp="name">Product Name</h1>
  <p itemProp="description">Product description...</p>
  <div itemProp="offers" itemScope itemType="https://schema.org/Offer">
    <meta itemProp="priceCurrency" content="PKR" />
    <span itemProp="price">1999</span>
  </div>
</div>`}
          </pre>
        </div>
      </div>

      <div className="mt-8">
        <Link href="/" className="text-blue-600 hover:underline">
          Back to Home
        </Link>
      </div>
    </div>
  );
}
