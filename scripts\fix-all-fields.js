const Typesense = require('typesense');
const { Pool } = require('pg');
require('dotenv').config();

// Create Typesense client
const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: 'localhost',
      port: '8109',
      protocol: 'http',
    },
  ],
  apiKey: 'xyz',
  connectionTimeoutSeconds: 2,
});

// Connect to PostgreSQL
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '**********************************************************',
});

async function fixAllFields() {
  try {
    console.log('Connecting to Typesense...');
    const health = await typesenseClient.health.retrieve();
    console.log('Typesense server is reachable:', health);

    console.log('Checking if products collection exists...');
    try {
      const collection = await typesenseClient.collections('products').retrieve();
      console.log(`Found collection: ${collection.name} with ${collection.num_documents} documents`);
      
      // Fetch products with all fields from database
      console.log('Fetching products with all fields from database...');
      const { rows: products } = await pool.query(`
        SELECT 
          p.id, 
          p."priceOld",
          p."featuredImage",
          p."standardizedTitle",
          c.id as category_id,
          c.name as category_name,
          c.slug as category_slug
        FROM product p
        LEFT JOIN product_categories_category pcc ON p.id = pcc."productId"
        LEFT JOIN category c ON pcc."categoryId" = c.id
      `);
      
      console.log(`Found ${products.length} products to update`);
      
      if (products.length === 0) {
        console.log('No products found. Nothing to update.');
        return;
      }
      
      // Update products in batches
      const BATCH_SIZE = 100;
      let successCount = 0;
      let errorCount = 0;
      let errors = [];
      
      console.log('Updating fields in Typesense...');
      
      for (let i = 0; i < products.length; i += BATCH_SIZE) {
        const batch = products.slice(i, i + BATCH_SIZE);
        console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(products.length / BATCH_SIZE)} (${batch.length} products)`);
        
        const updatePromises = batch.map(product => {
          // Only include fields that exist
          const updateData = {};
          
          if (product.priceOld !== undefined) {
            updateData.priceOld = product.priceOld || 0;
          }
          
          if (product.featuredImage !== undefined) {
            updateData.featuredImage = product.featuredImage || '';
          }
          
          if (product.standardizedTitle !== undefined) {
            updateData.standardizedTitle = product.standardizedTitle || '';
          }
          
          if (product.category_id !== undefined) {
            updateData.category_id = product.category_id ? product.category_id.toString() : '';
          }
          
          if (product.category_name !== undefined) {
            updateData.category_name = product.category_name || '';
          }
          
          if (product.category_slug !== undefined) {
            updateData.category_slug = product.category_slug || '';
          }
          
          return typesenseClient
            .collections('products')
            .documents(product.id.toString())
            .update(updateData)
            .catch(error => {
              console.error(`Error updating product ${product.id}:`, error.message);
              return { success: false, id: product.id, error: error.message };
            });
        });
        
        const results = await Promise.allSettled(updatePromises);
        
        const fulfilled = results.filter(r => r.status === 'fulfilled' && r.value.success !== false);
        const rejected = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && r.value.success === false));
        
        successCount += fulfilled.length;
        errorCount += rejected.length;
        
        if (rejected.length > 0) {
          errors = errors.concat(rejected.map(r => r.reason || r.value));
          console.log(`  ⚠️ Batch had ${rejected.length} errors`);
        } else {
          console.log(`  ✅ Batch updated successfully`);
        }
      }
      
      console.log(`Update summary: ${successCount} successes, ${errorCount} errors`);
      
      if (errorCount > 0) {
        console.error(`There were ${errorCount} errors during update`);
        if (errors.length > 10) {
          console.error('First 10 errors:', errors.slice(0, 10));
          console.error(`...and ${errors.length - 10} more errors`);
        } else {
          console.error('Errors:', errors);
        }
        
        return {
          success: false,
          message: `Some products failed to update (${errorCount} errors)`,
          errors: errors.slice(0, 10)
        };
      }
      
      return {
        success: true,
        message: `Successfully updated all fields for ${successCount} products`
      };
      
    } catch (error) {
      if (error.httpStatus === 404) {
        console.log('Products collection does not exist. Nothing to update.');
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('Error:', error);
    if (error.code === 'ECONNREFUSED') {
      console.error('Could not connect to Typesense server. Is it running?');
    }
    return {
      success: false,
      message: 'Failed to update fields',
      error: error.message
    };
  } finally {
    // Close the database connection
    await pool.end();
  }
}

async function main() {
  console.log('Starting field fix...');
  
  try {
    const result = await fixAllFields();
    
    if (result && result.success) {
      console.log('✅ ' + result.message);
      process.exit(0);
    } else if (result) {
      console.error('❌ ' + result.message);
      if (result.errors) {
        console.error('Errors:', result.errors);
      }
      process.exit(1);
    } else {
      console.log('No result returned');
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Fix failed with error:', error);
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
