"use server";

import { removeAuth<PERSON><PERSON><PERSON> } from "@/lib/cookies";
import { successResponse } from "@/lib/common/responses";

/**
 * Logout the user by removing the auth cookie
 */
export async function logoutUser() {
  try {
    await removeAuthCookie();
    return successResponse(null, "Logged out successfully", 200);
  } catch (error) {
    console.error("Logout error:", error);
    return successResponse(null, "Logged out successfully", 200);
  }
}
