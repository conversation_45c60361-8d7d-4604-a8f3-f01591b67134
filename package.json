{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx ./src/lib/migrate.ts", "db:migrate:users": "tsx ./src/lib/migrate-users.ts", "db:migrate:favorites": "tsx ./src/lib/migrate-favorites.ts", "typesense:sync": "node ./scripts/typesense-sync.js", "typesense:vector-sync": "node ./scripts/typesense-vector-sync.js", "typesense:compact-sync": "node ./scripts/typesense-compact-sync.js", "typesense:check": "node ./src/scripts/check-typesense.js", "typesense:delete": "node ./scripts/delete-typesense-collection.js", "typesense:delete-docs": "node ./scripts/delete-typesense-documents.js", "typesense:check-categories": "node ./scripts/check-categories.js", "typesense:fix-images": "node ./scripts/fix-featured-image.js", "typesense:fix-all": "node ./scripts/fix-all-fields.js"}, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "@tailwindcss/postcss": "^4.0.1", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@types/http-errors": "^2.0.4", "@xenova/transformers": "^2.17.2", "apexcharts": "^4.5.0", "bcrypt": "^5.1.1", "crypto-browserify": "^3.12.1", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.4", "drizzle-orm": "^0.39.2", "formik": "^2.4.6", "http-errors": "^2.0.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "next": "15.1.6", "next-auth": "^5.0.0-beta.28", "pg": "^8.13.1", "postgres": "^3.4.5", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-loader-spinner": "^6.1.6", "react-redux": "^9.2.0", "react-responsive-carousel": "^3.2.23", "react-toastify": "^11.0.3", "stream-browserify": "^3.0.0", "tsx": "^4.19.2", "typesense": "^2.0.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "ignore-loader": "^0.1.2", "postcss": "^8.5.1", "tailwindcss": "^4.0.1", "typescript": "^5"}}