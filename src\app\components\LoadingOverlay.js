"use client";

import React from "react";
import { FaSpinner } from "react-icons/fa";

/**
 * Full-screen loading overlay component
 * @param {Object} props Component props
 * @param {boolean} props.isVisible Whether the overlay is visible
 * @param {string} props.message Optional message to display
 */
export default function LoadingOverlay({
  isVisible,
  message = "Processing login...",
}) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
      <div className="text-center">
        <FaSpinner className="animate-spin text-4xl text-white mx-auto mb-4" />
        <p className="text-white font-medium">{message}</p>
      </div>
    </div>
  );
}
