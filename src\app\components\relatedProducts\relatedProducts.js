"use client";
import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aReg<PERSON><PERSON><PERSON> } from "react-icons/fa";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import Image from "next/image";
import phone from "../../assets/images/phones.png";
import computer from "../../assets/images/computers.png";
import sample1 from "../../assets/images/sample1.png";
import { renderStars } from "@/app/utils/renderStars";
import { useRouter } from "next/navigation";
import { toggleProductFavorite } from "@/app/utils/favoriteActions";
import { useFavorites } from "@/app/hooks/useFavorites";

const products = [
  {
    id: 1,
    category: "Samsung Electronics",
    name: "Samsung Galaxy S25 Silicone Case",
    oldPrice: "Rs 33,000",
    newPrice: "Rs 19,000",
    image: phone,
    rating: 4.5,
  },
  {
    id: 2,
    category: "Samsung Electronics",
    name: "MacBook Air M3",
    oldPrice: "Rs 250,000",
    newPrice: "Rs 190,000",
    image: computer,
    rating: 3.5,
  },
  {
    id: 3,
    category: "Samsung Electronics",
    name: "Samsung Galaxy S25 Silicone Case",
    oldPrice: "Rs 33,000",
    newPrice: "Rs 19,000",
    image: phone,
    rating: 2.5,
  },
  {
    id: 5,
    category: "Apple Electronics",
    name: "Apple iPhone 13 Pro Max",
    oldPrice: "Rs 250,000",
    newPrice: "Rs 120,000",
    image: sample1,
    rating: 4,
  },
  {
    id: 1,
    category: "Samsung Electronics",
    name: "Samsung Galaxy S25 Silicone Case",
    oldPrice: "Rs 33,000",
    newPrice: "Rs 19,000",
    image: phone,
    rating: 4.5,
  },
  {
    id: 2,
    category: "Samsung Electronics",
    name: "MacBook Air M3",
    oldPrice: "Rs 250,000",
    newPrice: "Rs 190,000",
    image: computer,
    rating: 3.5,
  },
  {
    id: 3,
    category: "Samsung Electronics",
    name: "Samsung Galaxy S25 Silicone Case",
    oldPrice: "Rs 33,000",
    newPrice: "Rs 19,000",
    image: phone,
    rating: 2.5,
  },
  {
    id: 5,
    category: "Apple Electronics",
    name: "Apple iPhone 13 Pro Max",
    oldPrice: "Rs 250,000",
    newPrice: "Rs 120,000",
    image: sample1,
    rating: 4,
  },
  {
    id: 1,
    category: "Samsung Electronics",
    name: "Samsung Galaxy S25 Silicone Case",
    oldPrice: "Rs 33,000",
    newPrice: "Rs 19,000",
    image: phone,
    rating: 4.5,
  },
  {
    id: 2,
    category: "Samsung Electronics",
    name: "MacBook Air M3",
    oldPrice: "Rs 250,000",
    newPrice: "Rs 190,000",
    image: computer,
    rating: 3.5,
  },
  {
    id: 3,
    category: "Samsung Electronics",
    name: "Samsung Galaxy S25 Silicone Case",
    oldPrice: "Rs 33,000",
    newPrice: "Rs 19,000",
    image: phone,
    rating: 2.5,
  },
  {
    id: 5,
    category: "Apple Electronics",
    name: "Apple iPhone 13 Pro Max",
    oldPrice: "Rs 250,000",
    newPrice: "Rs 120,000",
    image: sample1,
    rating: 4,
  },
];

const RelatedProducts = ({
  slidePercentage = null,
  currentSlideNumber,
  fullView = false,
  layout = null,
  categoryProduct,
  category,
}) => {
  const [currentSlide, setCurrentSlide] = useState(currentSlideNumber || 0);
  const { favorites, setFavorites, loading: favoritesLoading } = useFavorites();
  const rawCategory = category || "";
  const categoryName = rawCategory.replace(/-/g, " ");
  const router = useRouter();
  const handleNavigation = (name) => {
    const formattedCategory = encodeURIComponent(
      rawCategory.toLowerCase().replace(/ /g, "-")
    );
    const formattedProductName = encodeURIComponent(
      name.toLowerCase().replace(/ /g, "-")
    );
    router.push(`/${formattedCategory}/${formattedProductName}`);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % products.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + products.length) % products.length);
  };

  return (
    <div className="container mx-auto ">
      <div className="flex flex-row mb-6 gap-4">
        <h2 className="text-2xl font-bold transformaSansSemibold text-black  text-left">
          Related Products
        </h2>
      </div>
      {fullView ? (
        <div
          className={`grid grid-cols-1 md:grid-cols-2 ${
            layout === "list" ? "lg:grid-cols-1" : "lg:grid-cols-4"
          } gap-4`}
        >
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-white p-4 border border-[#EBEBEB] rounded-lg relative"
            >
              <span className="absolute top-2 left-2 bg-red-500 text-white px-1 py-1 text-xs rounded-lg">
                Bestseller
              </span>
              {favoritesLoading ? (
                <div className="absolute top-2 right-2 w-6 h-6 flex items-center justify-center">
                  <div className="animate-pulse w-4 h-4 rounded-full bg-gray-300"></div>
                </div>
              ) : (
                <FaHeart
                  onClick={() =>
                    toggleProductFavorite(product.id, setFavorites)
                  }
                  className={`absolute top-2 right-2 cursor-pointer ${
                    favorites.has(product.id)
                      ? "text-red-500"
                      : "text-gray-400 hover:text-red-500"
                  }`}
                />
              )}
              <Image
                src={product.image.src}
                alt={product.name}
                className="w-full h-40 object-contain rounded-md"
                width={500}
                height={500}
              />
              <div className="h-20">
                <h3 className="text-sm text-left text-gray-400 mt-2">
                  {product.category}
                </h3>
                <h3 className="text-lg font-semibold text-left text-gray-900">
                  {product.name}
                </h3>
              </div>
              <div className="flex items-center mb-2 mt-5">
                <div className="flex mr-2">{renderStars(product.rating)}</div>
                <span className="text-sm text-gray-600">{product.rating}</span>
              </div>
              <div className="flex flex-row gap-3 mt-2">
                <p className="text-gray-500 my-auto text-xs line-through">
                  {product.oldPrice}
                </p>
                <p className="text-red-500 font-bold">{product.newPrice}</p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="relative flex items-center">
          {/* Left Arrow */}
          <button
            onClick={prevSlide}
            className="absolute cursor-pointer hover:bg-gray-200 left-[-20px] top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full z-10 border border-gray-300"
          >
            <IoIosArrowBack size={24} color="black" />
          </button>

          <div className="w-full">
            <Carousel
              selectedItem={currentSlide}
              onChange={setCurrentSlide}
              showThumbs={false}
              showStatus={false}
              infiniteLoop
              centerMode
              centerSlidePercentage={slidePercentage}
              showArrows={false}
              showIndicators={false}
            >
              {categoryProduct?.map((product) => (
                <div
                  key={product.id}
                  className="bg-white p-4 border cursor-pointer border-[#EBEBEB] rounded-lg relative hover:shadow-lg hover:scale-[1.02] transition-all duration-200 group mr-2 mt-2 mb-4 ml-2"
                  onClick={() => handleNavigation(product.name)}
                >
                  <span className="absolute top-2 left-2 bg-[#F0381A] text-white px-2 py-1 text-xs rounded-lg Sora">
                    Bestseller
                  </span>
                  {favoritesLoading ? (
                    <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full">
                      <div className="bg-gray-100 p-1.5 rounded-full">
                        <div className="animate-pulse w-5 h-5 rounded-full bg-gray-300"></div>
                      </div>
                    </div>
                  ) : (
                    <div className="absolute top-2 right-2 bg-[#F5F5F5] bg-opacity-80 p-1 rounded-full cursor-pointer hover:bg-opacity-100 transition">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleProductFavorite(product.id, setFavorites);
                        }}
                        className="bg-gray-100 p-1.5 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        {favorites.has(product.id) ? (
                          <FaHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                        ) : (
                          <FaRegHeart className="text-pink-500 cursor-pointer text-lg w-5 h-5 hover:scale-110 transition-transform" />
                        )}
                      </button>
                    </div>
                  )}
                  <img
                    src={product.featuredImage}
                    alt={product.name}
                    className="w-full h-40  object-contain rounded-md"
                    width={500}
                    height={500}
                  />
                  <div className="h-17">
                    <h3 className="text-sm text-left text-gray-400 mt-4">
                      {categoryName}
                    </h3>
                    <h3 className="text-lg font-semibold Sora text-left text-gray-900 line-clamp-2 group-hover:text-[#F0381A] transition-colors">
                      {product.name}
                    </h3>
                  </div>
                  <div className="flex items-center flex-row mb-2 mt-5 ">
                    <div className="bg-[#F0F0F0] flex flex-row px-2 py-1 rounded-full">
                      <div className="flex mr-2 my-auto">{renderStars(4)}</div>
                      <span className="text-sm text-gray-600">{4}</span>
                    </div>
                  </div>
                  <div className="flex flex-row gap-3 mt-2 my-auto">
                    <p className="text-gray-500 my-auto text-base line-through">
                      {`Rs ${product.priceOld}`}
                    </p>
                    <p className="text-red-500 font-bold text-lg">
                      {`Rs ${product.price}`}
                    </p>
                  </div>
                </div>
              ))}
            </Carousel>
          </div>

          {/* Right Arrow */}
          <button
            onClick={nextSlide}
            className="absolute cursor-pointer hover:bg-gray-200 right-[-20px] top-1/2 transform -translate-y-1/2 p-3 bg-white rounded-full z-10 border border-gray-300"
          >
            <IoIosArrowForward size={24} color="black" />
          </button>
        </div>
      )}
    </div>
  );
};

export default RelatedProducts;
