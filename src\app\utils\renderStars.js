import { FaStar, FaStarHalfAlt, FaRegStar } from "react-icons/fa";

export const renderStars = (rating) => {
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    if (i <= Math.floor(rating)) {
      stars.push(<FaStar key={i} className="text-black w-3 h-3" />);
    } else if (i - rating < 1 && i - rating > 0) {
      stars.push(<FaStarHalfAlt key={i} className="text-black w-3 h-3" />);
    } else {
      stars.push(<FaRegStar key={i} className="text-black w-3 h-3" />);
    }
  }
  return stars;
};
