"use client";
import React from "react";
import { shapes } from "../../constants/shapes";
import { usePathname, useRouter } from "next/navigation";
import { useSelector } from "react-redux";

const CategoriesBar = ({
  categories,
  custompaddingx = null,
  background = null,
  marginTop = null,
  customDimensions = null,
  fixed = null,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const isHomePage = pathname === "/";
  const isScrolled = useSelector((state) => state.scroll.isScrolled);
  const handleNavigation = (slug) => {
    router.push(`/${slug}`);
  };

  return (
    <div
      className={`w-full ${
        fixed === "true" &&
        `fixed ${
          isScrolled || !isHomePage
            ? "mt-[4.6rem]"
            : `${marginTop ? marginTop : "mt-[11rem]"}`
        }`
      } z-10 top-0 left-0 right-0 ${
        background ? background : "bg-white"
      }  overflow-x-auto  scrollbar-hide overflow-y-hidden`}
    >
      <div
        className={`${
          custompaddingx ? custompaddingx : "px-10"
        } py-0 container mx-auto`}
      >
        <div className="flex items-center justify-center gap-4">
          {categories?.length === 0 ? (
            <div>No categories available</div>
          ) : (
            categories?.map((category) => {
              let shapeIndex;
              switch (category.slug) {
                case "computers-and-accessories":
                  shapeIndex = 0;
                  break;
                case "home-and-living":
                  shapeIndex = 7;
                  break;
                case "baby-and-kids":
                  shapeIndex = 2;
                  break;
                case "sports-and-outdoor":
                  shapeIndex = 3;
                  break;
                case "books-and-entertainment":
                  shapeIndex = 4;
                  break;
                case "automotive":
                  shapeIndex = 5;
                  break;
                case "grocery":
                  shapeIndex = 6;
                  break;
                case "miscellaneous":
                  shapeIndex = 7;
                  break;
                case "phone-and-gadgets":
                  shapeIndex = 8;
                  break;
                case "electronics-and-appliances":
                  shapeIndex = 9;
                  break;
                case "fashion-and-clothing":
                  shapeIndex = 10;
                  break;
                case "health-and-beauty":
                  shapeIndex = 11;
                  break;
                default:
                  shapeIndex = category.id % shapes.length;
              }

              const shapeSvg = shapes[shapeIndex];
              return (
                <div
                  key={category.id}
                  className="flex flex-col cursor-pointer items-center min-w-[80px] max-w-[100px]"
                  onClick={() => handleNavigation(category?.slug)}
                >
                  <div
                    className={`relative flex justify-center items-center ${
                      (isScrolled && fixed === "true") || !isHomePage
                        ? "w-11 h-11"
                        : `${customDimensions ? customDimensions : "w-18 h-18"}`
                    }`}
                  >
                    <div className="absolute inset-0 flex justify-center items-center">
                      {shapeSvg}
                    </div>
                    <img
                      src={
                        category.image
                          ? category.image
                          : "https://mofets.com/wp-content/uploads/2024/05/1fbe8131b5-removebg-preview.png"
                      }
                      alt={category.name}
                      className={`relative object-contain ${
                        (isScrolled && fixed === "true") || !isHomePage
                          ? "w-8 h-8"
                          : "w-12 h-12"
                      }`}
                    />
                  </div>
                  <p className="text-xs text-black font-bold mb-2 transformaSansNormal text-center mt-2 leading-tight break-words w-full overflow-hidden whitespace-normal">
                    {category.name}
                  </p>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoriesBar;
