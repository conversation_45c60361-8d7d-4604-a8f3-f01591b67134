const Typesense = require('typesense');
require('dotenv').config();

// Create Typesense client
const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: 'localhost',
      port: '8109',
      protocol: 'http',
    },
  ],
  apiKey: 'xyz',
  connectionTimeoutSeconds: 2,
});

async function deleteProductsCollection() {
  try {
    console.log('Connecting to Typesense...');
    const health = await typesenseClient.health.retrieve();
    console.log('Typesense server is reachable:', health);

    console.log('Checking if products collection exists...');
    try {
      const collection = await typesenseClient.collections('products').retrieve();
      console.log(`Found collection: ${collection.name} with ${collection.num_documents} documents`);
      
      console.log('Deleting products collection...');
      await typesenseClient.collections('products').delete();
      console.log('✅ Products collection successfully deleted!');
      
    } catch (error) {
      if (error.httpStatus === 404) {
        console.log('Products collection does not exist. Nothing to delete.');
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('Error:', error);
    if (error.code === 'ECONNREFUSED') {
      console.error('Could not connect to Typesense server. Is it running?');
    }
  }
}

// Run the delete function
deleteProductsCollection().catch(console.error);
