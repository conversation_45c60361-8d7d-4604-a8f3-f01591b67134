"use server";

import typesenseServerClient from "@/lib/typesense-server";
import { db } from "@/lib/db";
import {
  products,
  categories,
  product_categories_category,
} from "@/lib/schema";
import { eq } from "drizzle-orm";
import { generateProductEmbeddingsBatch } from "@/lib/embeddings-server";

// Defining the schema for the products collection
const productsCollectionSchema = {
  name: "products",
  fields: [
    { name: "id", type: "int32" },
    { name: "name", type: "string" },
    { name: "description", type: "string", optional: true },
    { name: "price", type: "float" },
    { name: "priceOld", type: "float", optional: true },
    { name: "featuredImage", type: "string", optional: true },
    { name: "keywords", type: "string[]", optional: true },
    { name: "standardizedTitle", type: "string", optional: true },
    { name: "category_id", type: "string", optional: true },
    { name: "category_name", type: "string", optional: true },
    { name: "category_slug", type: "string", optional: true },
    { name: "embedding", type: "float[]", optional: true, num_dim: 384 }, // Vector field for embeddings
  ],
  default_sorting_field: "price",
};

// Function to sync all products from PostgreSQL to Typesense
export async function syncProductsToTypesense() {
  try {
    try {
      await typesenseServerClient.collections("products").retrieve();
      console.log("Collection already exists");
    } catch (error) {
      if (error.httpStatus === 404) {
        await typesenseServerClient
          .collections()
          .create(productsCollectionSchema);
        console.log("Created new collection: products");
      } else {
        throw error;
      }
    }

    // Fetching all products with their categories
    const productsWithCategories = await db
      .select({
        id: products.id,
        name: products.name,
        description: products.description,
        price: products.price,
        priceOld: products.priceOld,
        featuredImage: products.featuredImage,
        keywords: products.keywords,
        standardizedTitle: products.standardizedTitle,
        category_id: categories.id,
        category_name: categories.name,
        category_slug: categories.slug,
      })
      .from(products)
      .innerJoin(
        product_categories_category,
        eq(products.id, product_categories_category.productId)
      )
      .innerJoin(
        categories,
        eq(product_categories_category.categoryId, categories.id)
      );

    if (!productsWithCategories.length) {
      return { success: false, message: "No products found to sync" };
    }

    // Format documents for Typesense (without embeddings first)
    const documentsWithoutEmbeddings = productsWithCategories.map(
      (product) => ({
        id: product.id.toString(),
        name: product.name || "",
        description: product.description || "",
        price: product.price || 0,
        priceOld: product.priceOld || 0,
        featuredImage: product.featuredImage || "",
        keywords: product.keywords || [],
        standardizedTitle: product.standardizedTitle || "",
        category_id: product.category_id ? product.category_id.toString() : "",
        category_name: product.category_name || "",
        category_slug: product.category_slug || "",
      })
    );

    console.log("Generating embeddings for products...");
    // Generate embeddings for all products
    const documentsWithEmbeddings = await generateProductEmbeddingsBatch(
      documentsWithoutEmbeddings,
      5 // Smaller batch size for embedding generation
    );

    // Format final documents with embeddings
    const documents = documentsWithEmbeddings.map((product) => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      priceOld: product.priceOld,
      featuredImage: product.featuredImage,
      keywords: product.keywords,
      standardizedTitle: product.standardizedTitle,
      category_id: product.category_id,
      category_name: product.category_name,
      category_slug: product.category_slug,
      embedding: product.embedding || [], // Include embedding or empty array if generation failed
    }));

    console.log(
      `Prepared ${documents.length} documents with embeddings for Typesense`
    );

    // Import documents to Typesense in batches
    const BATCH_SIZE = 50000;
    let successCount = 0;
    let errorCount = 0;
    let errors = [];

    // Process in batches
    for (let i = 0; i < documents.length; i += BATCH_SIZE) {
      const batch = documents.slice(i, i + BATCH_SIZE);
      console.log(
        `Processing batch ${i / BATCH_SIZE + 1} of ${Math.ceil(
          documents.length / BATCH_SIZE
        )} (${batch.length} products)`
      );

      try {
        const batchResponse = await typesenseServerClient
          .collections("products")
          .documents()
          .import(batch, { action: "upsert" });

        // Count successes and errors
        const batchErrors = batchResponse.filter(
          (res) => res.success === false
        );
        errorCount += batchErrors.length;
        successCount += batch.length - batchErrors.length;

        if (batchErrors.length > 0) {
          errors = errors.concat(batchErrors);
          console.log(`Batch had ${batchErrors.length} errors`);
        }
      } catch (error) {
        console.error(`Error importing batch:`, error.message);
        errorCount += batch.length;
        errors.push({
          message: `Failed to import batch starting at index ${i}`,
          error: error.message,
        });
      }
    }

    console.log(
      `Import summary: ${successCount} successes, ${errorCount} errors`
    );

    if (errorCount > 0) {
      console.error(`There were ${errorCount} errors during import`);
      // Only show the first 10 errors to avoid overwhelming the console
      if (errors.length > 10) {
        console.error("First 10 errors:", errors.slice(0, 10));
      } else {
        console.error("Errors:", errors);
      }

      return {
        success: false,
        message: `Some products failed to sync (${errorCount} errors)`,
        errors: errors.slice(0, 10), // Only return the first 10 errors to avoid response size issues
      };
    }

    return {
      success: true,
      message: `Successfully synced ${documents.length} products to Typesense`,
    };
  } catch (error) {
    console.error("Error syncing products to Typesense:", error);
    return {
      success: false,
      message: "Failed to sync products to Typesense",
      error: error.message,
    };
  }
}
